<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToInsurancePapersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('insurance_papers', function (Blueprint $table) {
            // Add missing columns based on the controller logic
            if (!Schema::hasColumn('insurance_papers', 'Code')) {
                $table->integer('Code')->nullable()->after('id');
            }
            if (!Schema::hasColumn('insurance_papers', 'Date')) {
                $table->date('Date')->nullable()->after('Code');
            }
            if (!Schema::hasColumn('insurance_papers', 'Draw')) {
                $table->decimal('Draw', 10, 2)->nullable()->after('Date');
            }
            if (!Schema::hasColumn('insurance_papers', 'From')) {
                $table->string('From')->nullable()->after('Draw');
            }
            if (!Schema::hasColumn('insurance_papers', 'To')) {
                $table->string('To')->nullable()->after('From');
            }
            if (!Schema::hasColumn('insurance_papers', 'FromEn')) {
                $table->string('FromEn')->nullable()->after('To');
            }
            if (!Schema::hasColumn('insurance_papers', 'ToEn')) {
                $table->string('ToEn')->nullable()->after('FromEn');
            }
            if (!Schema::hasColumn('insurance_papers', 'Note')) {
                $table->text('Note')->nullable()->after('ToEn');
            }
            if (!Schema::hasColumn('insurance_papers', 'Due_Date')) {
                $table->date('Due_Date')->nullable()->after('Note');
            }
            if (!Schema::hasColumn('insurance_papers', 'Amount')) {
                $table->decimal('Amount', 15, 2)->nullable()->after('Due_Date');
            }
            if (!Schema::hasColumn('insurance_papers', 'Status')) {
                $table->integer('Status')->default(0)->after('Amount');
            }
            if (!Schema::hasColumn('insurance_papers', 'Coin')) {
                $table->integer('Coin')->nullable()->after('Status');
            }
            if (!Schema::hasColumn('insurance_papers', 'Cost_Center')) {
                $table->integer('Cost_Center')->nullable()->after('Coin');
            }
            if (!Schema::hasColumn('insurance_papers', 'Account')) {
                $table->integer('Account')->nullable()->after('Cost_Center');
            }
            if (!Schema::hasColumn('insurance_papers', 'Bank')) {
                $table->integer('Bank')->nullable()->after('Account');
            }
            if (!Schema::hasColumn('insurance_papers', 'File')) {
                $table->string('File')->nullable()->after('Bank');
            }
            if (!Schema::hasColumn('insurance_papers', 'User')) {
                $table->integer('User')->nullable()->after('File');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('insurance_papers', function (Blueprint $table) {
            // Drop columns in reverse order
            $columns = ['User', 'File', 'Bank', 'Account', 'Cost_Center', 'Coin', 'Status', 'Amount', 'Due_Date', 'Note', 'ToEn', 'FromEn', 'To', 'From', 'Draw', 'Date', 'Code'];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('insurance_papers', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
}
