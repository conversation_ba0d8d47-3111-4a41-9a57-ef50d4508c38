<?php

namespace App\Http\Controllers;

use Str;
use Illuminate\Http\Request;
use App\Models\AcccountingManual;
use App\Models\UsersMoves;
use App\Models\CostCenter;
use App\Models\Coins;
use App\Models\Branches;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\Admin;
use App\Models\PaymentVoucher;
use App\Models\PaymentVoucherDetails;
use App\Models\ReciptVoucher;
use App\Models\ReciptVoucherDetails;
use App\Models\OpeningEntries;
use App\Models\OpeningEntriesDetails;
use App\Models\ChecksTypes;
use App\Models\ExportChecks;
use App\Models\IncomChecks;
use App\Models\ShippingCompany;
use App\Models\SafesBanks;
use App\Models\Stores;
use App\Models\SafeTransfers;
use App\Models\ItemsGroups;
use App\Models\Employess;
use App\Models\Vendors;
use App\Models\Customers;
use App\Models\AssetsExpenses;
use App\Models\Assets;
use App\Models\Taxes;
use App\Models\CrmDefaultData;
use App\Models\InsurancePaper;
use App\Models\SalesDefaultData;
use App\Models\AccountsDefaultData;
use App\Models\PurchasesDefaultData;
use App\Models\StoresDefaultData;
use App\Models\Event;
use App\Models\Notifications;
use App\Models\MaintainceDefaultData;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use DB;
use DateTime;
class AccountsController extends Controller
{


function __construct()
{

$this->middleware('permission:الدليل المحاسبي', ['only' => ['AccountingManualPage','AddAccount','EditAccount','DeleteAccount']]);
$this->middleware('permission:مراكز التكلفه', ['only' =>['CostCentersPage','AddCostCenters','EditCostCenters','DeleteCostCenters']]);
$this->middleware('permission:العملات', ['only' =>['CoinsPage','AddCoins','EditCoins','DeleteCoins']]);
$this->middleware('permission:انواع الشيكات', ['only' =>['Checks_TypePage','AddChecks_Type','EditChecks_Type','DeleteChecks_Type']]);
$this->middleware('permission:اضافه قيد يومي', ['only' =>['JournalizingPage','AddJournalizing','JournalizingPrint']]);
$this->middleware('permission:جدول القيود اليوميه', ['only' =>['JournalizingSechdule','EditJournalizing','DeleteJournalizing','PostEditJournalizing']]);
$this->middleware('permission:اضافه سند قبض', ['only' =>['Receipt_VoucherPage','AddReceipt_Voucher','Receipt_VoucherPrint']]);
$this->middleware('permission:جدول سند القبض', ['only' =>['Receipt_VoucherSechdule','EditReceipt_Voucher','DeleteReceipt_Voucher','PostEditReceipt_Voucher']]);
$this->middleware('permission:اضافه سند صرف', ['only' =>['Payment_VoucherPage','AddPayment_Voucher','Payment_VoucherPrint']]);
$this->middleware('permission:جدول سند الصرف', ['only' =>['Payment_VoucherSechdule','EditPayment_Voucher','DeletePayment_Voucher','PostEditPayment_Voucher']]);
$this->middleware('permission:اضافه قيد افتتاحي', ['only' =>['OpeningEntriesPage','AddOpeningEntries','Opening_EntriesPrint']]);
$this->middleware('permission:جدول القيود الافتتاحيه', ['only' =>['Opening_EntriesSechdule','EditOpening_Entries','DeleteOpening_Entries','PostEditOpening_Entries']]);
$this->middleware('permission:الشيكات الصادره', ['only' =>['Exporting_ChecksPage','AddExporting_Checks','EditExporting_Checks','DeleteExportingChecks','ReasonExportChecks','TransExportingChecks','PayExportingChecks']]);
$this->middleware('permission:الشيكات الوارده', ['only' =>['Incoming_checksPage','AddIncoming_checks','EditIncoming_checks','DeleteIncoming_checks','ReasonIncoming_checks','TransIncoming_checks','PayIncomingChecks']]);
$this->middleware('permission:الخزائن و البنوك', ['only' =>['Safes_BanksPage','AddSafes_Banks','EditSafes_Banks','DeleteSafes_Banks']]);
$this->middleware('permission:الاصول', ['only' =>['AssetsPage','AddAssets','DeleteAssets']]);
$this->middleware('permission:تحويلات الخزائن', ['only' =>['SafesTransferPage','AddSafeTransfer']]);
$this->middleware('permission:جدول تحويلات الخزائن', ['only' =>['SafesTransferSechdulePage']]);
$this->middleware('permission:مصاريف اصول', ['only' =>['AssetExpensesPage','AddAssetsExpenses']]);
$this->middleware('permission:وصل امانه', ['only' =>['Insurance_PaperPage','AddInsurancePaper','DeleteInsurancePaper','RecivedInurance']]);

}



    // ===  Accounting Manual  =====
    public function AccountingManualPage(){
        $parents=AcccountingManual::orderBy('Code','asc')->where('Parent',0)->get();
        $Groups=ItemsGroups::all();
         return view('admin.Accounts.AccountingManual',[
             'parents'=>$parents,
             'Groups'=>$Groups,

         ]);
    }

      public function AddAccount(){

        $data= $this->validate(request(),[
             'Name'=>'required',
             'Type'=>'required',



               ],[
            'Name.required' => trans('admin.NameRequired'),
            'Type.required' => trans('admin.AccountTypeRequired'),


         ]);

            if(!empty(request('Parent'))){
         $data['Parent']=request('Parent');

            $count=AcccountingManual::orderBy('Code','desc')->where('Parent',request('Parent'))->count();
            $code=AcccountingManual::orderBy('Code','desc')->where('Parent',request('Parent'))->first();
            $codee=AcccountingManual::find(request('Parent'));

                if($count == 0){

                $x=$codee->Code.'01';
             $data['Code']=(int) $x ;

                }else{


         $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;
            }
                $x= $codee->Code.$NewXY;



                  $data['Code']=(int) $x;

                }


          }else{

               $data['Parent']=0;
           $code=AcccountingManual::orderBy('id','desc')->where('Parent',0)->first();

        if(!empty($code)){
          $data['Code']=$code->Code + 1 ;
        }else{

         $data['Code']=1;
        }

          }



         $data['Name']=request('Name');

          if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
               $data['NameEn']=request('Name');

          }
         $data['Type']=request('Type');
         $data['Note']=request('Note');
         $data['Account_Code']=request('Account_Code');
         $data['Pro_Group']=request('Pro_Group');
         $data['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($data);


          if(request('Parent') == 28){
             $Acc=AcccountingManual::orderBy('id','desc')->first();

                    $res=SafesBanks::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }


         $dataa['Code']=$Code;
         $dataa['Date']=date('Y-m-d');
         $dataa['Name']=request('Name');

                     if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
               $dataa['NameEn']=request('Name');

          }


         $dataa['Type']=1;
         $dataa['Note']=request('Note');
         $dataa['Account']=$Acc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;

         SafesBanks::create($dataa);
          }elseif(request('Parent') == 29){

                   $Acc=AcccountingManual::orderBy('id','desc')->first();

                    $res=SafesBanks::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }


         $dataa['Code']=$Code;
         $dataa['Date']=date('Y-m-d');
         $dataa['Name']=request('Name');
        if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
        $dataa['NameEn']=request('Name');
          }
         $dataa['Type']=2;
         $dataa['Note']=request('Note');
         $dataa['Account']=$Acc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;
            SafesBanks::create($dataa);

          }


             if(request('Parent') == 27){
             $Acc=AcccountingManual::orderBy('id','desc')->first();

                    $res=Stores::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }


         $dataa['Code']=$Code;
         $dataa['Date']=date('Y-m-d');
         $dataa['Time']=date("h:i:s a", time());
         $dataa['Name']=request('Name');
         if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
        $dataa['NameEn']=request('Name');
          }
         $dataa['Phone']=null;
         $dataa['Address']=null;
         $dataa['Account']=$Acc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;

         Stores::create($dataa);
          }


            if(request('Parent') == 37){
             $Acc=AcccountingManual::orderBy('id','desc')->first();

                    $res=Vendors::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }


        $dataa['Code']=$Code;
         $dataa['Name']=request('Name');
            if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
        $dataa['NameEn']=request('Name');
          }
         $dataa['Phone']=request('Phone');
         $dataa['Phone2']=request('Phone2');
         $dataa['Commercial_Register']=request('Commercial_Register');
         $dataa['Tax_Card']=request('Tax_Card');
         $dataa['Price_Level']=1;
         $dataa['Account']=$Acc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;

         Vendors::create($dataa);
          }


           if(request('Parent') == 24){
             $Acc=AcccountingManual::orderBy('id','desc')->first();

                    $res=Customers::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }


         $dataa['Code']=$Code;
         $dataa['Name']=request('Name');
            if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
        $dataa['NameEn']=request('Name');
          }
         $dataa['Price_Level']=1;
         $dataa['Account']=$Acc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;

         Customers::create($dataa);
          }

            if(request('Parent') == 97){
             $Acc=AcccountingManual::orderBy('id','desc')->first();

         $dataa['Name']=request('Name');
           if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
        $dataa['NameEn']=request('Name');
          }
      $dataa['Account']=$Acc->id;

         ShippingCompany::create($dataa);
          }

           if(request('Parent') == 39){
             $Acc=AcccountingManual::orderBy('id','desc')->first();

                    $res=Taxes::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

         $data['Code']=$Code;
         $data['Name']=request('Name');
             if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
        $data['NameEn']=request('Name');
          }
         $data['Rate']=0;
         $data['Type']=2;
         $data['Hide']=0;
         $data['Account']=$Acc->id;

         Taxes::create($data);
          }




           $Acc=AcccountingManual::find(request('Parent'));


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الدليل المحاسبي';
           $dataUser['ScreenEn']='Accounting_Manual';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');

              if(!empty(request('NameEn'))){
          $dataUser['ExplainEn']=request('NameEn');
          }else{
        $dataUser['ExplainEn']=request('Name');

          }
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditAccount(){

        $data= $this->validate(request(),[
             'Name'=>'required',

               ],[
            'Name.required' => trans('admin.NameRequired'),
         ]);

         $data['Name']=request('Name');
           $data['NameEn']=request('NameEn');
         $data['Note']=request('Note');
         $data['Account_Code']=request('Account_Code');
    $data['Pro_Group']=request('Pro_Group');
         AcccountingManual::where('id',request('ID'))->update($data);


           $Acc=AcccountingManual::find(request('ID'));


             if($Acc->Parent == 28){

    $safe=SafesBanks::where('Account',$Acc->id)->first();

         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');
         $dataa['Note']=request('Note');

         SafesBanks::where('id',$safe->id)->update($dataa);

          }elseif($Acc->Parent == 29){


    $safe=SafesBanks::where('Account',$Acc->id)->first();

         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');
         $dataa['Note']=request('Note');

         SafesBanks::where('id',$safe->id)->update($dataa);


          }


               if($Acc->Parent == 27){

    $safe=Stores::where('Account',$Acc->id)->first();

         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');

         Stores::where('id',$safe->id)->update($dataa);

          }

              if($Acc->Parent == 37){

    $safe=Vendors::where('Account',$Acc->id)->first();

         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');

         Vendors::where('id',$safe->id)->update($dataa);

          }

          if($Acc->Parent == 24){

    $safe=Customers::where('Account',$Acc->id)->first();

         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');

         Customers::where('id',$safe->id)->update($dataa);

          }
          if($Acc->Parent == 97){

    $safe=ShippingCompany::where('Account',$Acc->id)->first();

         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');

         ShippingCompany::where('id',$safe->id)->update($dataa);

          }


           if($Acc->Parent == 39){

    $safe=Taxes::where('Account',$Acc->id)->first();

         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');

         Taxes::where('id',$safe->id)->update($dataa);

          }




           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الدليل المحاسبي';
           $dataUser['ScreenEn']='Accounting Manual';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }

       public function DeleteAccount($id){

     $count=AcccountingManual::orderBy('id','desc')->where('Parent',$id)->count();

          if($count != 0){

        session()->flash('error',trans('admin.U_Cant_Delete_Any_Account_Has_Traffic'));
        return back();

          }


          $xh=GeneralDaily::where('Account',$id)->orderBy('id','desc')->first();

                  if(!empty($xh)){

         session()->flash('error',trans('admin.CantDeleteAnyItemHasTraffic'));
        return back();

         }


        $del=AcccountingManual::find($id);


        if($del->Parent == 28 or $del->Parent == 29){
         $safe=SafesBanks::where('Account',$del->id)->delete();
        }

        if($del->Parent == 27){
         $safe=Stores::where('Account',$del->id)->delete();
        }

        if($del->Parent == 37){
         $safe=Vendors::where('Account',$del->id)->delete();
        }

         if($del->Parent == 24){
         $safe=Customers::where('Account',$del->id)->delete();
        }

        if($del->Parent == 97){
         $safe=ShippingCompany::where('Account',$del->id)->delete();
        }

        if($del->Parent == 39){
         $safe=Taxes::where('Account',$del->id)->delete();
        }


           $xc=(string)$del->Code;

        if($xc[0] == 6){

         $safe=Employess::where('Account_Emp',$del->id)->delete();

        }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']='الدليل المحاسبي';
           $dataUser['ScreenEn']='Accounting Manual';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Name;
           $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

    //======  Cost Centers =======
    public function CostCentersPage(){
        $items=CostCenter::all();
         return view('admin.Accounts.CostCenters',['items'=>$items]);
    }

     public function AddCostCenters(){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),
            'English_Name.required' => trans('admin.English_NameRequired'),

         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');


         CostCenter::create($data);



           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='مراكز التكلفه';
           $dataUser['ScreenEn']='Cost Centers';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');

              if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');

          }



           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditCostCenters($id){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),
            'English_Name.required' => trans('admin.English_NameRequired'),

         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');

           CostCenter::where('id',$id)->update($data);

                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='مراكز التكلفه';
           $dataUser['ScreenEn']='Cost Centers';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteCostCenters($id){
                                $Maint=MaintainceDefaultData::orderBy('id','desc')->first();
             if($Maint->Cost_Center == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();

         }

        $del=CostCenter::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='مراكز التكلفه';
           $dataUser['ScreenEn']='Cost Centers';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


    //======  Coins =======
        public function CoinsPage(){
        $items=Coins::all();
         return view('admin.Accounts.Coins',['items'=>$items]);
    }

     public function AddCoins(){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',
             'Draw'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),
            'English_Name.required' => trans('admin.English_NameRequired'),
            'Draw.required' => trans('admin.DrawRequired'),

         ]);



         $data['Arabic_Name']=request('Arabic_Name');


         $data['English_Name']=request('English_Name');
         $data['Draw']=request('Draw');
         $data['Symbol']=request('Symbol');
         $data['Code']=request('Code');


         Coins::create($data);



           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
         $dataUser['Screen']='العملات';
           $dataUser['ScreenEn']='Coins';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
                 if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');

          }


           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditCoins($id){


        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',
             'Draw'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),
            'English_Name.required' => trans('admin.English_NameRequired'),
            'Draw.required' => trans('admin.DrawRequired'),

         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');
         $data['Draw']=request('Draw');
         $data['Symbol']=request('Symbol');
             $data['Code']=request('Code');
           Coins::where('id',$id)->update($data);

                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='العملات';
           $dataUser['ScreenEn']='Coins';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteCoins($id){

         $Accounts=AccountsDefaultData::orderBy('id','desc')->first();
                $Stores=StoresDefaultData::orderBy('id','desc')->first();
                $Purchases=PurchasesDefaultData::orderBy('id','desc')->first();
                $Sales=SalesDefaultData::orderBy('id','desc')->first();
  $Maint=MaintainceDefaultData::orderBy('id','desc')->first();
         if($Accounts->Coin == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();

         }
           if($Stores->Coin == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();

         }
           if($Purchases->Coin == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();

         }
           if($Sales->Coin == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();

         }
           if($Maint->Coin == $id){

        session()->flash('error',trans('admin.Cant_Delete_Default_Data'));
        return back();

         }

        $del=Coins::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                            $dataUser['Screen']='العملات';
           $dataUser['ScreenEn']='Coins';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


        //======  Checks Types =======
        public function Checks_TypePage(){
        $items=ChecksTypes::all();
         return view('admin.Accounts.ChecksTypes',['items'=>$items]);
    }

     public function AddChecks_Type(){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),
            'English_Name.required' => trans('admin.English_NameRequired'),

         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');


         ChecksTypes::create($data);



           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

          $dataUser['Screen']='انواع الشيكات';
           $dataUser['ScreenEn']='Checks Type';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
                   if(!empty(request('NameEn'))){
           $dataUser['ExplainEn']=request('English_Name');
          }else{
        $dataUser['ExplainEn']=request('Arabic_Name');

          }



           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditChecks_Type($id){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

               ],[
            'Arabic_Name.required' => trans('admin.Arabic_NameRequired'),
            'English_Name.required' => trans('admin.English_NameRequired'),

         ]);



         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');

           ChecksTypes::where('id',$id)->update($data);

                $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='انواع الشيكات';
           $dataUser['ScreenEn']='Checks Type';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit ';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteChecks_Type($id){

        $del=ChecksTypes::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                    $dataUser['Screen']='انواع الشيكات';
           $dataUser['ScreenEn']='Checks Type';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete ';

            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->Arabic_Name;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


    // ======  Journalizing =======

       public function JournalizingPage(){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
$Branchs=Branches::all();
          $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

           $Accounts=AcccountingManual::where('Type',1)->get();

         return view('admin.Accounts.Journalizing',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
         'Branchs'=>$Branchs,
         'Accounts'=>$Accounts,
         ]);
    }

         public function AddJournalizing(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);


                      $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{

                   $Status=0;

              }


        $ID = DB::table('journalizings')->insertGetId(

        array(


            'Type' => 'القيود اليومية',
            'TypeEn' => 'Journalizing',
            'Code_Type' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Branch' => request('Branch'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
            'Status' => $Status,

        )
    );


               $c= DB::select("SELECT last_value FROM journalizings_arr_seq");
      $f=array_shift($c);
      $z=end($f);

        DB::table('journalizings')->where('id',$ID)->update(['Code_Type'=>$z]);


    $CodeT=$z;



                 if($DefAcc->Sure_Recipts != 0) {
   $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='قيد يومي يحتاج للتأكيد';
         $notii['Noti_En_Name']='Journalizings Need Confirmation';
         $notii['Type']='القيد اليومي';
  $notii['TypeEn']='Journalizings';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.Journalizings_Need_Confirmation'));




              }

         $Debitor =request('Debitor');
         $Creditor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');



        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTS['Joun_ID']=$ID;
        $PRODUCTS['Debitor']=$Debitor[$i];
        $PRODUCTS['Creditor']=$Creditor[$i];
        $PRODUCTS['Account']=$Account[$i];
        $PRODUCTS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTS);
     if($DefAcc->Sure_Recipts == 0) {
        $Gen['Code']=request('Code');
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='القيود اليومية';
        $Gen['TypeEn']='Journalizing';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=$Creditor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * $Creditor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);
     }

             }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='القيود اليومية';
           $dataUser['ScreenEn']='Journalizing';
           $dataUser['Type']='اضاقه جديده';
           $dataUser['TypeEn']='New Add';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));

            if(request('SP') == 1){
            return redirect('JournalizingPrint/'.$ID);
            }elseif(request('SP') == 0){
             return back();
            }



    }

       public function JournalizingSechdule(){

          $items=Journalizing::paginate(50);

         return view('admin.Sechdules.JournalizingSechdules',[
             'items'=>$items,

         ]);
    }

         public function JournalizingSechduleFilter(){

             $from=request('From');
             $to=request('To');
             $CodeType=request('Type');

          $items=Journalizing::whereBetween('Date',[$from,$to])

            ->when(!empty($CodeType), function ($query) use ($CodeType) {
        return $query->where('Type',$CodeType);

                })
             ->paginate(50);


               $items->appends(request()->query());

         return view('admin.Sechdules.JournalizingSechdules',[
             'items'=>$items,

         ]);
    }

    public function EditJournalizing($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
           $item=Journalizing::find($id);
        $details=JournalizingDetails::where('Joun_ID',$item->id)->get();
$Branchs=Branches::all();


            $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 1) {
         if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('JournalizingSechdule');

                    }
              }
         return view('admin.Accounts.EditJournalizing',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,
         'Branchs'=>$Branchs,
         ]);
    }

     public function DeleteJournalizing($id){

        $del=Journalizing::find($id);

                    Notifications::where('Type_Code',$del->Code)->where('Type','القيد اليومي')->delete();
         GeneralDaily::where('Code',$del->Code)->where('Type','القيود اليومية')->delete();

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='القيود اليومية';
           $dataUser['ScreenEn']='Journalizing';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

       public function PostEditJournalizing(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',


               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);

            $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{

                   $Status=0;

                                                 $del=Journalizing::find(request('ID'));
           Notifications::where('Type_Code',$del->Code)->where('Type','القيد اليومي')->delete();


              }

         $del=Journalizing::find(request('ID'));
         GeneralDaily::where('Code',$del->Code)->where('Type','القيود اليومية')->delete();
          $del->delete();

        $ID = DB::table('journalizings')->insertGetId(

        array(


            'Type' => 'القيود اليومية',
            'TypeEn' => 'Journalizing',
            'Code_Type' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Coin'),
            'Coin' => request('Draw'),
            'Branch' => request('Branch'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
            'Status' => $Status,

        )
    );




               $c= DB::select("SELECT last_value FROM journalizings_arr_seq");
      $f=array_shift($c);
      $z=end($f);

        DB::table('journalizings')->where('id',$ID)->update(['Code_Type'=>$z]);


    $CodeT=$z;


                      if($DefAcc->Sure_Recipts != 0) {


         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='قيد يومي يحتاج للتأكيد';
         $notii['Noti_En_Name']='Journalizings Need Confirmation';
         $notii['Type']='القيد اليومي';
  $notii['TypeEn']='Journalizings';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.Journalizings_Need_Confirmation'));


              }



         $Debitor =request('Debitor');
         $Creditor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTS['Joun_ID']=$ID;
        $PRODUCTS['Debitor']=$Debitor[$i];
        $PRODUCTS['Creditor']=$Creditor[$i];
        $PRODUCTS['Account']=$Account[$i];
        $PRODUCTS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTS);

          if($DefAcc->Sure_Recipts == 0) {
        $Gen['Code']=request('Code');
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='القيود اليومية';
        $Gen['TypeEn']='Journalizing';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=$Creditor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * $Creditor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);
          }

             }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='القيود اليومية';
           $dataUser['ScreenEn']='Journalizing';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
            return redirect('JournalizingSechdule');

    }

        public function JournalizingPrint($id){

          $item=Journalizing::find($id);
         $details=JournalizingDetails::where('Joun_ID',$item->id)->get();
         return view('admin.Sechdules.JournalizingPrint',[
             'item'=>$item,
             'details'=>$details,

         ]);
    }

      public function SureJournalizing($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
           $item=Journalizing::find($id);
        $details=JournalizingDetails::where('Joun_ID',$item->id)->get();
$Branchs=Branches::all();

                   if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('JournalizingSechdule');

                    }
         return view('admin.Accounts.SureJournalizing',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,
         'Branchs'=>$Branchs,
         ]);
    }

    public function PostSureJournalizing(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',


               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);

            $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();



         $del=Journalizing::find(request('ID'));
         GeneralDaily::where('Code',$del->Code)->where('Type','القيود اليوميه')->delete();
          Notifications::where('Type_Code',$del->Code)->where('Type','القيد اليومي')->delete();
          $del->delete();





        $ID = DB::table('journalizings')->insertGetId(

        array(


            'Type' => 'القيود اليومية',
            'TypeEn' => 'Journalizing',
            'Code_Type' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Coin'),
            'Coin' => request('Draw'),
            'Branch' => request('Branch'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
            'Status' => 1,

        )
    );



               $c= DB::select("SELECT last_value FROM journalizings_arr_seq");
      $f=array_shift($c);
      $z=end($f);

        DB::table('journalizings')->where('id',$ID)->update(['Code_Type'=>$z]);


    $CodeT=$z;


         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='تأكيد القيد اليومي';
         $notii['Noti_En_Name']='Journalizings  Confirmed';
         $notii['Type']='القيد اليومي';
         $notii['TypeEn']='Journalizings';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

        notify()->success(trans('admin.Journalizings_Confirmed'));




         $Debitor =request('Debitor');
         $Creditor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTS['Joun_ID']=$ID;
        $PRODUCTS['Debitor']=$Debitor[$i];
        $PRODUCTS['Creditor']=$Creditor[$i];
        $PRODUCTS['Account']=$Account[$i];
        $PRODUCTS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTS);


        $Gen['Code']=request('Code');
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='القيود اليومية';
        $Gen['TypeEn']='Journalizing';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=$Creditor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * $Creditor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);
          }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']='القيود اليومية';
           $dataUser['ScreenEn']='Journalizing';
           $dataUser['Type']='تأكيد';
           $dataUser['TypeEn']='Sure';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
            return redirect('JournalizingSechdule');

    }

    //=== Recipt Voucher  ====
     public function Receipt_VoucherPage(){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();

          $res=ReciptVoucher::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

            $Accounts=AcccountingManual::where('Type',1)->get();
         return view('admin.Accounts.Receipt_Voucher',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'Accounts'=>$Accounts,

         ]);
    }

      public function AddReceipt_Voucher(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);



                        $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{

                   $Status=0;

              }


            $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=null;
             }



        $IDD = DB::table('recipt_vouchers')->insertGetId(

        array(


            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
             'Status' => $Status,
             'File' => $zFile,

        )
    );



                       $c= DB::select("SELECT last_value FROM recipt_vouchers_arr_seq");
      $f=array_shift($c);
      $z=end($f);

    $CodeT=$z;



                           $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts != 0) {


         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']=' سند قبض يحتاج للتأكيد';
         $notii['Noti_En_Name']='Receipt Voucher Need Confirmation';
         $notii['Type']='سند قبض';
  $notii['TypeEn']='Receipt Voucher';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.Receipt_Voucher_Need_Confirmation'));

         }


         $Debitor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['RV_ID']=$IDD;
        $PRODUCTS['Creditor']=$Debitor[$z];
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];


         ReciptVoucherDetails::create($PRODUCTS);


             }

              if($DefAcc->Sure_Recipts == 0) {
           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Creditor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$Debitor[$i];
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$Debitor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $Debitor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }


        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Total_Creditor');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Total_Creditor');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Total_Creditor');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);
              }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']= 'سند قبض';
           $dataUser['ScreenEn']='Receipt Voucher';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
                  if(request('SP') == 1){
            return redirect('Receipt_VoucherPrint/'.$IDD);
            }elseif(request('SP') == 0){
             return back();
            }

    }

     public function Receipt_VoucherSechdule(){

          $items=ReciptVoucher::paginate(50);
                 $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
         return view('admin.Sechdules.ReceiptVoucherSechdule',[
             'items'=>$items,
             'Safes'=>$Safes,

         ]);
    }


     public function ReceiptVoucherSechduleFilter(){
          $from=request('From');
             $to=request('To');
             $CodeType=request('Code');
             $Safe=request('Safe');


          $items=ReciptVoucher::whereBetween('Date',[$from,$to])
                         ->when(!empty($CodeType), function ($query) use ($CodeType) {
        return $query->where('Code',$CodeType);

                })

                         ->when(!empty($Safe), function ($query) use ($Safe) {
        return $query->where('Safe',$Safe);

                })
              ->paginate(50);
                 $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();

           $items->appends(request()->query());
         return view('admin.Sechdules.ReceiptVoucherSechdule',[
             'items'=>$items,
             'Safes'=>$Safes,

         ]);
    }

       public function EditReceipt_Voucher($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
           $item=ReciptVoucher::find($id);
        $details=ReciptVoucherDetails::where('RV_ID',$item->id)->get();


                     $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 1) {
                    if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Receipt_VoucherSechdule');

                    }

              }

         return view('admin.Accounts.EditReceiptVoucher',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,

         ]);
    }

     public function DeleteReceipt_Voucher($id){

        $del=ReciptVoucher::find($id);

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
                    Notifications::where('Type_Code',$del->Code)->where('Type','سند قبض')->delete();

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']= 'سند قبض';
           $dataUser['ScreenEn']='Receipt Voucher';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

       public function PostEditReceipt_Voucher(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);

             $del=ReciptVoucher::find(request('ID'));

           $res=Journalizing::where('Code_Type',$del->Code)->where('Type','سند قبض')->first();
             GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
        Journalizing::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
            if(!empty($res)){
            $Code=$res->Code;



            }



                        $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{
                       Notifications::where('Type_Code',$del->Code)->where('Type','سند قبض')->delete();
                   $Status=0;
              }
  $del->delete();
            $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=request('Files');
             }



        $IDD = DB::table('recipt_vouchers')->insertGetId(

        array(

            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
              'Status' => $Status,
              'File' => $zFile,

        )
    );



                               $c= DB::select("SELECT last_value FROM recipt_vouchers_arr_seq");
      $f=array_shift($c);
      $z=end($f);

    $CodeT=$z;


                  $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts != 0) {


         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']=' سند قبض يحتاج للتأكيد';
         $notii['Noti_En_Name']='Receipt Voucher Need Confirmation';
         $notii['Type']='سند قبض';
  $notii['TypeEn']='Receipt Voucher';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.Receipt_Voucher_Need_Confirmation'));

         }

         $Debitor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['RV_ID']=$IDD;
        $PRODUCTS['Creditor']=$Debitor[$z];
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];


         ReciptVoucherDetails::create($PRODUCTS);


             }

      if($DefAcc->Sure_Recipts == 0) {
        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' =>  'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Creditor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$Debitor[$i];
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$Debitor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $Debitor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }


        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Total_Creditor');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Total_Creditor');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Total_Creditor');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

      }
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
             $dataUser['Screen']= 'سند قبض';
           $dataUser['ScreenEn']='Receipt Voucher';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
         return redirect('Receipt_VoucherSechdule');


    }


       public function Receipt_VoucherPrint($id){

          $item=ReciptVoucher::find($id);
         $details=ReciptVoucherDetails::where('RV_ID',$item->id)->get();
         return view('admin.Sechdules.Receipt_VoucherPrint',[
             'item'=>$item,
             'details'=>$details,

         ]);
    }


       public function SureReceipt_Voucher($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
           $item=ReciptVoucher::find($id);
        $details=ReciptVoucherDetails::where('RV_ID',$item->id)->get();

                           $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 1) {
                    if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Receipt_VoucherSechdule');

                    }

              }


         return view('admin.Accounts.SureReceipt_Voucher',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,

         ]);
    }

         public function PostSureReceipt_Voucher(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);

             $del=ReciptVoucher::find(request('ID'));

        GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
        Journalizing::where('Code_Type',$del->Code)->where('Type','سند قبض')->delete();
         Notifications::where('Type_Code',$del->Code)->where('Type','سند قبض')->delete();
           $res=Journalizing::where('Code_Type',$del->Code)->where('Type','سند قبض')->first();


              if(!empty($res)){
                              $Code=$res->Code;


  }else{

                  $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        }


        $del->delete();



        $IDD = DB::table('recipt_vouchers')->insertGetId(

        array(


            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
              'Status' => 1,

        )
    );



       $c= DB::select("SELECT last_value FROM recipt_vouchers_arr_seq");
      $f=array_shift($c);
      $z=end($f);
       $CodeT=$z;

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='تأكيد سند قبض';
         $notii['Noti_En_Name']='Receipt Voucher  Confirmed';
         $notii['Type']='سند قبض';
         $notii['TypeEn']='Receipt Voucher';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

        notify()->success(trans('admin.Receipt_Voucher_Confirmed'));

         $Debitor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['RV_ID']=$IDD;
        $PRODUCTS['Creditor']=$Debitor[$z];
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];


         ReciptVoucherDetails::create($PRODUCTS);


             }


        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' =>  'سند قبض',
            'TypeEn' => 'Receipt Voucher',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Creditor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$Debitor[$i];
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']= 'سند قبض';
        $Gen['TypeEn']='Receipt Voucher';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$Debitor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $Debitor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }


        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Total_Creditor');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);
        $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();
        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']= 'سند قبض';
        $Genn['TypeEn']='Receipt Voucher';
        $Genn['Debitor']=request('Total_Creditor');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * request('Total_Creditor');
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
            $dataUser['Screen']= 'سند قبض';
           $dataUser['ScreenEn']='Receipt Voucher';
           $dataUser['Type']='تأكيد';
           $dataUser['TypeEn']='Sure';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
         return redirect('Receipt_VoucherSechdule');


    }


    // ====  Payment Voucher  ===
    public function Payment_VoucherPage(){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();

          $res=PaymentVoucher::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }
               $Accounts=AcccountingManual::where('Type',1)->get();

         return view('admin.Accounts.Payment_Voucher',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'Accounts'=>$Accounts,

         ]);
    }

    public function AddPayment_Voucher(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);

         $debt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Debitor');
           $crdt=GeneralDaily::where('Account',request('Safe'))->get()->sum('Creditor');
            $dif=$debt - $crdt ;
            $SafyFatora=request('Total_Debaitor');
            $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();


          if($dif < $SafyFatora){

              session()->flash('error',trans('admin.SafeNotEnoughMoney'));
              return back();

          }



                       $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{

                   $Status=0;
              }

           $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=null;
             }



        $IDD = DB::table('payment_vouchers')->insertGetId(

        array(


            'Date' => request('Date'),
                'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Note' => request('Note'),
            'Branch' => $Safe->Branch,
              'Status' => $Status,
              'File' => $zFile,

        )
    );




               $c= DB::select("SELECT last_value FROM payment_vouchers_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;





        $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts != 0) {

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']=' سند صرف يحتاج للتأكيد';
         $notii['Noti_En_Name']='Payment Voucher Need Confirmation';
         $notii['Type']='سند صرف';
  $notii['TypeEn']='Payment Voucher';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.Payment_Voucher_Need_Confirmation'));

         }



         $Debitor =request('Debitor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['PV_ID']=$IDD;
        $PRODUCTS['Debitor']=$Debitor[$z];
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];
        $PRODUCTS['Date']= request('Date');
        $PRODUCTS['Time']=date("h:i:s a", time());
        $PRODUCTS['Cost_Center']=request('Cost_Center');
        $PRODUCTS['Coin']=request('Coin');
        $PRODUCTS['User']=auth()->guard('admin')->user()->id;
        $PRODUCTS['Branch']=$Safe->Branch;
        $PRODUCTS['Safe']=request('Safe');


         PaymentVoucherDetails::create($PRODUCTS);


             }

         if($DefAcc->Sure_Recipts == 0) {
           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'سند صرف',
            'TypeEn' => 'Payment Voucher',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
                'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Debaitor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$Debitor[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='سند صرف';
        $Gen['TypeEn']='Payment Voucher';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }


        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=request('Total_Debaitor');
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='سند صرف';
        $Genn['TypeEn']='Payment Voucher';
        $Genn['Debitor']=0;
        $Genn['Creditor']=request('Total_Debaitor');
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * request('Total_Debaitor');
        $Genn['Account']=request('Safe');
        $Genn['Branch']=$Safe->Branch;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

         }
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='سند صرف';
           $dataUser['ScreenEn']='Payment Voucher';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
                     if(request('SP') == 1){
            return redirect('Payment_VoucherPrint/'.$IDD);
            }elseif(request('SP') == 0){
             return back();
            }

    }

    public function Payment_VoucherSechdule(){

          $items=PaymentVoucher::paginate(50);
                  $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
         return view('admin.Sechdules.PaymentVoucherSechdule',[
             'items'=>$items,
             'Safes'=>$Safes,

         ]);
    }

         public function PaymentVoucherSechduleFilter(){
          $from=request('From');
             $to=request('To');
             $CodeType=request('Code');
             $Safe=request('Safe');


          $items=PaymentVoucher::whereBetween('Date',[$from,$to])
                         ->when(!empty($CodeType), function ($query) use ($CodeType) {
        return $query->where('Code',$CodeType);

                })

                         ->when(!empty($Safe), function ($query) use ($Safe) {
        return $query->where('Safe',$Safe);

                })
              ->paginate(50);
                 $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();

               $items->appends(request()->query());
         return view('admin.Sechdules.PaymentVoucherSechdule',[
             'items'=>$items,
             'Safes'=>$Safes,

         ]);
    }


    public function EditPayment_Voucher($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
          $item=PaymentVoucher::find($id);
          $details=PaymentVoucherDetails::where('PV_ID',$item->id)->get();



                        $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 1) {
                    if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Payment_VoucherSechdule');

                    }

              }

         return view('admin.Accounts.EditPaymentVoucher',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,

         ]);
    }

     public function DeletePayment_Voucher($id){

        $del=PaymentVoucher::find($id);

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند صرف')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','سند صرف')->delete();
           Notifications::where('Type_Code',$del->Code)->where('Type','سند صرف')->delete();

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='سند صرف';
           $dataUser['ScreenEn']='Payment Voucher';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

     public function PostEditPayment_Voucher(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);


        $del=PaymentVoucher::find(request('ID'));

             if(!empty($del->Code)){
          $res=Journalizing::where('Code_Type',$del->Code)->where('Type','سند صرف')->first();
          GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند صرف')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','سند صرف')->delete();
           if(!empty($res)){
            $Code=$res->Code;




           }
            $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();

             }

                       $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{

                   $Status=0;
                       Notifications::where('Type_Code',$del->Code)->where('Type','سند صرف')->delete();
              }


             $del->delete();
  $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=request('Files');
             }

        $IDD = DB::table('payment_vouchers')->insertGetId(

        array(


            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Note' => request('Note'),
  'Branch' => $Safe->Branch,
               'Status' => $Status,
               'File' => $zFile,
        )
    );



                        $c= DB::select("SELECT last_value FROM payment_vouchers_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;



         $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts != 0) {

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']=' سند صرف يحتاج للتأكيد';
         $notii['Noti_En_Name']='Payment Voucher Need Confirmation';
         $notii['Type']='سند صرف';
  $notii['TypeEn']='Payment Voucher';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.Payment_Voucher_Need_Confirmation'));

         }


         $Debitor =request('Debitor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['PV_ID']=$IDD;
        $PRODUCTS['Debitor']=$Debitor[$z];
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];
       $PRODUCTS['Date']= request('Date');
        $PRODUCTS['Time']=date("h:i:s a", time());
        $PRODUCTS['Cost_Center']=request('Cost_Center');
        $PRODUCTS['Coin']=request('Coin');
        $PRODUCTS['User']=auth()->guard('admin')->user()->id;
        $PRODUCTS['Branch']=$Safe->Branch;
        $PRODUCTS['Safe']=request('Safe');

         PaymentVoucherDetails::create($PRODUCTS);


             }



   if($DefAcc->Sure_Recipts == 0) {
        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'سند صرف',
            'TypeEn' => 'Payment Voucher',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Debaitor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$Debitor[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='سند صرف';
        $Gen['TypeEn']='Payment Voucher';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }


        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=request('Total_Debaitor');
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);


        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='سند صرف';
        $Genn['TypeEn']='Payment Voucher';
        $Genn['Debitor']=0;
        $Genn['Creditor']=request('Total_Debaitor');
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * request('Total_Debaitor');
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);
   }

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='سند صرف';
           $dataUser['ScreenEn']='Payment Voucher';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
               return redirect('Payment_VoucherSechdule');

    }

      public function Payment_VoucherPrint($id){

          $item=PaymentVoucher::find($id);
             $details=PaymentVoucherDetails::where('PV_ID',$item->id)->get();
         return view('admin.Sechdules.Payment_VoucherPrint',[
             'item'=>$item,
             'details'=>$details,

         ]);
    }

     public function SurePayment_Voucher($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
          $item=PaymentVoucher::find($id);
          $details=PaymentVoucherDetails::where('PV_ID',$item->id)->get();


                              $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 1) {
                    if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Payment_VoucherSechdule');

                    }

              }

         return view('admin.Accounts.SurePayment_Voucher',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,

         ]);
    }


     public function PostSurePayment_Voucher(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Safe'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);


        $del=PaymentVoucher::find(request('ID'));
          $res=Journalizing::where('Code_Type',$del->Code)->where('Type','سند صرف')->first();
                 GeneralDaily::where('Code_Type',$del->Code)->where('Type','سند صرف')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','سند صرف')->delete();
          Notifications::where('Type_Code',$del->Code)->where('Type','سند صرف')->delete();

          if(!empty($res)){
                  $Code=$res->Code;


  }else{

                  $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        }


       $Safe=SafesBanks::where('Account',request('Safe'))->orderBy('id','desc')->first();


    $del->delete();



        $IDD = DB::table('payment_vouchers')->insertGetId(

        array(


            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Safe' => request('Safe'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Note' => request('Note'),
  'Branch' => $Safe->Branch,
               'Status' => 1,
        )
    );


                            $c= DB::select("SELECT last_value FROM payment_vouchers_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;


               $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='تأكيد سند صرف';
         $notii['Noti_En_Name']='Payment Voucher  Confirmed';
         $notii['Type']='سند صرف';
         $notii['TypeEn']='Payment Voucher';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

        notify()->success(trans('admin.Payment_Voucher_Confirmed'));


         $Debitor =request('Debitor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['PV_ID']=$IDD;
        $PRODUCTS['Debitor']=$Debitor[$z];
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];
       $PRODUCTS['Date']=date('Y-m-d');
        $PRODUCTS['Time']=date("h:i:s a", time());
        $PRODUCTS['Cost_Center']=request('Cost_Center');
        $PRODUCTS['Coin']=request('Coin');
        $PRODUCTS['User']=auth()->guard('admin')->user()->id;
        $PRODUCTS['Branch']=$Safe->Branch;
        $PRODUCTS['Safe']=request('Safe');

         PaymentVoucherDetails::create($PRODUCTS);


             }



        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'سند صرف',
            'TypeEn' => 'Payment Voucher',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
               'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Debaitor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$Debitor[$i];
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='سند صرف';
        $Gen['TypeEn']='Payment Voucher';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=0;
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }


        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=request('Total_Debaitor');
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);


        $Genn['Branch']=$Safe->Branch;
        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='سند صرف';
        $Genn['TypeEn']='Payment Voucher';
        $Genn['Debitor']=0;
        $Genn['Creditor']=request('Total_Debaitor');
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * request('Total_Debaitor');
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='سند صرف';
           $dataUser['ScreenEn']='Payment Voucher';
           $dataUser['Type']='تأكيد';
           $dataUser['TypeEn']='Sure';
            $dataUser['Explain']=$CodeT;
            $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
               return redirect('Payment_VoucherSechdule');

    }

     // ====  Opening Entries  ===
     public function OpeningEntriesPage(){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();

          $res=OpeningEntries::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }
               $Accounts=AcccountingManual::where('Type',1)->get();
         return view('admin.Accounts.OpeningEntries',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'Accounts'=>$Accounts,

         ]);
    }

     public function AddOpeningEntries(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Capital'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);

                         $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{

                   $Status=0;
              }

        $IDD = DB::table('opening_entries')->insertGetId(

        array(


            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Capital' => request('Capital'),
            'SecAccount' => request('SecAccount'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
             'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
   'Status' => $Status,
        )
    );


                                   $c= DB::select("SELECT last_value FROM opening_entries_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;




              if($DefAcc->Sure_Recipts != 0) {

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='القيد الافتتاحي يحتاج للتأكيد';
         $notii['Noti_En_Name']='Opening Entries Need Confirmation';
         $notii['Type']='القيد الافتتاحي';
  $notii['TypeEn']='Opening Entries';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.OpeningEntries_Need_Confirmation'));

         }


         $Debitor =request('Debitor');
         $Creditor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['OP_ID']=$IDD;
        $PRODUCTS['Debitor']=abs($Debitor[$z]);
        $PRODUCTS['Creditor']=abs($Creditor[$z]);
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];


         OpeningEntriesDetails::create($PRODUCTS);


             }

       if($DefAcc->Sure_Recipts == 0) {
           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

         if(!empty(request('SecAccount'))){

             $acc=request('SecAccount');
         }else{
             $acc=45;
         }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'القيد الإفتتاحي',
            'TypeEn' => 'Opening Entries',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$Debitor[$i];
        $PRODUCTSS['Creditor']=$Creditor[$i];
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='القيد الإفتتاحي';
        $Gen['TypeEn']= 'Opening Entries';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=$Creditor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * $Creditor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }

            $Dif=  request('Total_Debaitor') - request('Total_Creditor') ;
        if(request('Capital') < 0){

        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=abs($Dif);
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=$acc;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='القيد الإفتتاحي';
        $Genn['TypeEn']= 'Opening Entries';
        $Genn['Debitor']=abs($Dif);
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * abs($Dif);
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']= $acc;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

        }else{

        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$Dif;
        $PRODUCTSSS['Account']=$acc;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='القيد الإفتتاحي';
        $Genn['TypeEn']= 'Opening Entries';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$Dif;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * $Dif;
        $Genn['Account']= $acc;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

        }

       }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='القيد الإفتتاحي';
           $dataUser['ScreenEn']= 'Opening Entries';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
                      if(request('SP') == 1){
            return redirect('Opening_EntriesPrint/'.$IDD);
            }elseif(request('SP') == 0){
             return back();
            }

    }

     public function Opening_EntriesSechdule(){

          $items=OpeningEntries::paginate(50);

         return view('admin.Sechdules.OpeningEntriesSechdules',[
             'items'=>$items,

         ]);
    }

      public function OpeningEntriesSechduleFilter(){

           $from=request('From');
             $to=request('To');
             $CodeType=request('Code');

          $items=OpeningEntries::whereBetween('Date',[$from,$to])
                       ->when(!empty($CodeType), function ($query) use ($CodeType) {
        return $query->where('Code',$CodeType);

                })
              ->paginate(50);
          $items->appends(request()->query());
         return view('admin.Sechdules.OpeningEntriesSechdules',[
             'items'=>$items,

         ]);
    }

    public function EditOpening_Entries($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
           $item=OpeningEntries::find($id);
        $details=OpeningEntriesDetails::where('OP_ID',$item->id)->get();


                             $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 1) {
                    if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Opening_EntriesSechdule');

                    }

              }



         return view('admin.Accounts.EditOpeningEntries',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,

         ]);
    }

      public function DeleteOpening_Entries($id){

        $del=OpeningEntries::find($id);

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->delete();
            Notifications::where('Type_Code',$del->Code)->where('Type','القيد الافتتاحي')->delete();
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='القيد الإفتتاحي';
           $dataUser['ScreenEn']= 'Opening Entries';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

       public function PostEditOpening_Entries(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Capital'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);


          $del=OpeningEntries::find(request('ID'));
          $res=Journalizing::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->first();
                GeneralDaily::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->delete();
           Notifications::where('Type_Code',$del->Code)->where('Type','القيد الافتتاحي')->delete();
           if(!empty($res)){
            $Code=$res->Code;


           }
    $del->delete();


               if(!empty(request('SecAccount'))){

             $acc=request('SecAccount');
         }else{
             $acc=45;
         }


                      $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 0) {

              $Status=1;
              }else{

                   $Status=0;
              }


        $IDD = DB::table('opening_entries')->insertGetId(

        array(


            'Date' => request('Date'),
             'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Capital' => request('Capital'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
             'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
            'SecAccount' => request('SecAccount'),
             'Status' => $Status,

        )
    );


                                           $c= DB::select("SELECT last_value FROM opening_entries_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;




    if($DefAcc->Sure_Recipts != 0) {

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='القيد الافتتاحي يحتاج للتأكيد';
         $notii['Noti_En_Name']='Opening Entries Need Confirmation';
         $notii['Type']='القيد الافتتاحي';
  $notii['TypeEn']='Opening Entries';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->warning(trans('admin.OpeningEntries_Need_Confirmation'));

         }

         $Debitor =request('Debitor');
         $Creditor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['OP_ID']=$IDD;
        $PRODUCTS['Debitor']=abs($Debitor[$z]);
        $PRODUCTS['Creditor']=abs($Creditor[$z]);
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];


         OpeningEntriesDetails::create($PRODUCTS);


             }


         if($DefAcc->Sure_Recipts == 0) {

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'القيد الإفتتاحي',
            'TypeEn' =>  'Opening Entries',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$Debitor[$i];
        $PRODUCTSS['Creditor']=$Creditor[$i];
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='القيد الإفتتاحي';
        $Gen['TypeEn']= 'Opening Entries';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=$Creditor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * $Creditor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }

            $Dif=  request('Total_Debaitor') - request('Total_Creditor') ;
        if(request('Capital') < 0){

        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=abs($Dif);
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=$acc;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='القيد الإفتتاحي';
        $Genn['TypeEn']= 'Opening Entries';
        $Genn['Debitor']=0;
        $Genn['Creditor']=abs($Dif);
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * abs($Dif);
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']= $acc;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

        }else{

        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$Dif;
        $PRODUCTSSS['Account']=$acc;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='القيد الإفتتاحي';
        $Genn['TypeEn']= 'Opening Entries';
        $Genn['Debitor']=$Dif;
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * $Dif;
        $Genn['Account']= $acc;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

        }

         }
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='القيد الإفتتاحي';
           $dataUser['ScreenEn']= 'Opening Entries';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
              return redirect('Opening_EntriesSechdule');

    }

 public function Opening_EntriesPrint($id){

          $item=OpeningEntries::find($id);
            $details=OpeningEntriesDetails::where('OP_ID',$item->id)->get();
         return view('admin.Sechdules.Opening_EntriesPrint',[
             'item'=>$item,
             'details'=>$details,

         ]);
    }

  public function SureOpening_Entries($id){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $Accounts=AcccountingManual::where('Type',1)->get();
           $item=OpeningEntries::find($id);
        $details=OpeningEntriesDetails::where('OP_ID',$item->id)->get();

                                   $DefAcc=AccountsDefaultData::orderBy('id','desc')->first();

              if($DefAcc->Sure_Recipts == 1) {
                    if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('Opening_EntriesSechdule');

                    }

              }

         return view('admin.Accounts.SureOpening_Entries',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Accounts'=>$Accounts,
             'item'=>$item,
             'details'=>$details,

         ]);
    }


      public function PostSureOpeningEntries(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Capital'=>'required',

               ],[
            'Date.required' => trans('admin.DateRequired'),
            'Safe.required' => trans('admin.SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),

         ]);


          $del=OpeningEntries::find(request('ID'));
          $res=Journalizing::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->first();
           GeneralDaily::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','القيد الإفتتاحي')->delete();
              Notifications::where('Type_Code',$del->Code)->where('Type','القيد الافتتاحي')->delete();
        if(!empty($res)){

            $Code=$res->Code;



        }else{

                  $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        }
    $del->delete();


               if(!empty(request('SecAccount'))){

             $acc=request('SecAccount');
         }else{
             $acc=45;
         }


        $IDD = DB::table('opening_entries')->insertGetId(

        array(


            'Date' => request('Date'),
             'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Capital' => request('Capital'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
             'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),
            'SecAccount' => request('SecAccount'),
             'Status' => 1,

        )
    );


     $c= DB::select("SELECT last_value FROM opening_entries_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;




         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='تأكيد القيد الافتتاحي';
         $notii['Noti_En_Name']='Opening Entries  Confirmed';
         $notii['Type']='القيد الافتتاحي';
          $notii['TypeEn']='Opening Entries';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']=null;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=null;
         Notifications::create($notii);

                notify()->success(trans('admin.OpeningEntries_Confirmed'));



         $Debitor =request('Debitor');
         $Creditor =request('Creditor');
         $Account =request('Account');
         $Statement =request('Statement');


        for($z=0 ; $z < count($Debitor) ; $z++){

        $PRODUCTS['OP_ID']=$IDD;
        $PRODUCTS['Debitor']=abs($Debitor[$z]);
        $PRODUCTS['Creditor']=abs($Creditor[$z]);
        $PRODUCTS['Account']=$Account[$z];
        $PRODUCTS['Statement']=$Statement[$z];


         OpeningEntriesDetails::create($PRODUCTS);


             }


        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'القيد الإفتتاحي',
            'TypeEn' =>  'Opening Entries',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Total_Debaitor'),
            'Total_Creditor' => request('Total_Creditor'),
            'Note' => request('Note'),

        )
    );




        for($i=0 ; $i < count($Debitor) ; $i++){

        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$Debitor[$i];
        $PRODUCTSS['Creditor']=$Creditor[$i];
        $PRODUCTSS['Account']=$Account[$i];
        $PRODUCTSS['Statement']=$Statement[$i];


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='القيد الإفتتاحي';
        $Gen['TypeEn']= 'Opening Entries';
        $Gen['Debitor']=$Debitor[$i];
        $Gen['Creditor']=$Creditor[$i];
        $Gen['Statement']=$Statement[$i];
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $Debitor[$i];
        $Gen['Creditor_Coin']=request('Draw') * $Creditor[$i];
        $Gen['Account']=$Account[$i];
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


             }

            $Dif=  request('Total_Debaitor') - request('Total_Creditor') ;
        if(request('Capital') < 0){

        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=abs($Dif);
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=$acc;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='القيد الإفتتاحي';
        $Genn['TypeEn']= 'Opening Entries';
        $Genn['Debitor']=0;
        $Genn['Creditor']=abs($Dif);
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * abs($Dif);
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']= $acc;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

        }else{

        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$Dif;
        $PRODUCTSSS['Account']=$acc;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=request('Date');
        $Genn['Type']='القيد الإفتتاحي';
        $Genn['TypeEn']= 'Opening Entries';
        $Genn['Debitor']=$Dif;
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * $Dif;
        $Genn['Account']= $acc;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

        }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='القيد الإفتتاحي';
           $dataUser['ScreenEn']= 'Opening Entries';
           $dataUser['Type']='تأكيد';
           $dataUser['TypeEn']='Sure';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
              return redirect('Opening_EntriesSechdule');

    }


     // ====  Exporting Checks  ===
     public function Exporting_ChecksPage(){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $ChecksTypes=ChecksTypes::all();
          $items=ExportChecks::paginate(50);

          $res=ExportChecks::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

         return view('admin.Accounts.ExportChecks',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'ChecksTypes'=>$ChecksTypes,
             'items'=>$items,

         ]);
    }


         public function ExportingChecksSechduleFilter(){


              $from=request('From');
             $to=request('To');
             $CodeType=request('Code');
             $Account=request('Account');
             $Due_Date=request('Due_Date');




                    $items=ExportChecks::whereBetween('Date',[$from,$to])

                                  ->when(!empty($CodeType), function ($query) use ($CodeType) {
        return $query->where('Code',$CodeType);

                })

                                  ->when(!empty($Account), function ($query) use ($Account) {
        return $query->where('Account',$Account);

                })

                                  ->when(!empty($Due_Date), function ($query) use ($Due_Date) {
        return $query->where('Due_Date',$Due_Date);

                })

          ->paginate(50);
          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $ChecksTypes=ChecksTypes::all();


          $res=ExportChecks::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }
             $items->appends(request()->query());
         return view('admin.Accounts.ExportChecks',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'ChecksTypes'=>$ChecksTypes,
             'items'=>$items,

         ]);
    }

     public function AddExporting_Checks(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',

             'Account'=>'required',
             'Check_Type'=>'required',
             'Bank'=>'required',
             'Check_Num'=>'required',
             'Due_Date'=>'required',
             'Amount'=>'required',

               ],[

            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),
            'Account.required' => trans('admin.AccountRequired'),
            'Check_Type.required' => trans('admin.Check_TypeRequired'),
            'Bank.required' => trans('admin.BankRequired'),
            'Check_Num.required' => trans('admin.Check_NumRequired'),
            'Due_Date.required' => trans('admin.Due_DateRequired'),
            'Amount.required' => trans('admin.AmountRequired'),

         ]);

          $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=null;
             }

          $imageImg=request()->file('Image');
          if($imageImg){
            $image_nameImg=Str::random(20);
            $extImg=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameImg=$image_nameImg .'.' . $extImg ;
            $upload_pathImg='SafeTransferFiles/';
            $image_urlImg=$upload_pathImg.$image_full_nameImg;
            $successImg=$imageImg->move($upload_pathImg,$image_full_nameImg);
                   }


             if(!empty($image_urlImg)){

                 $zImage=$image_urlImg;

             }else{
                 $zImage=null;
             }


        $IDD = DB::table('export_checks')->insertGetId(

        array(


            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Check_Num' => request('Check_Num'),
            'Due_Date' => request('Due_Date'),
            'Amount' => request('Amount'),
            'Status' =>0,
            'Reason' =>null,
            'Check_Type' => request('Check_Type'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Account' => request('Account'),
            'Bank' => request('Bank'),
            'Image' => $zImage,
            'Signture_Name' => request('Signture_Name'),
            'Bank_Branch' => request('Bank_Branch'),
            'Pay_Account' =>42,
            'Bene_Account' =>null,
            'File' =>$zFile,
            'User' =>auth()->guard('admin')->user()->id,

        )
    );


             $c= DB::select("SELECT last_value FROM export_checks_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;

                  $event['Start_Date']=request('Due_Date');
         $event['End_Date']=request('Due_Date');
         $event['Event_Ar_Name']='صرف شيك صادر';
         $event['Event_En_Name']='Cashing an exporting check';
         $event['Type']='شيك صادر';
         $event['Type_ID']=null;
         $event['Type_Code']=$CodeT;
         $event['Emp']=null;
         $event['Client']=request('Account');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);


           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'الشيكات الصادرة',
            'TypeEn' => 'Exporting Checks',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Account');
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الصادرة';
        $Gen['TypeEn']= 'Exporting Checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=42;
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الصادرة';
        $Gen['TypeEn']= 'Exporting Checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=42;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الصادرة';
           $dataUser['ScreenEn']= 'Exporting Checks';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditExporting_Checks($id){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',

             'Account'=>'required',
             'Check_Type'=>'required',
             'Bank'=>'required',
             'Check_Num'=>'required',
             'Due_Date'=>'required',
             'Amount'=>'required',

               ],[

            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),
            'Account.required' => trans('admin.AccountRequired'),
            'Check_Type.required' => trans('admin.Check_TypeRequired'),
            'Bank.required' => trans('admin.BankRequired'),
            'Check_Num.required' => trans('admin.Check_NumRequired'),
            'Due_Date.required' => trans('admin.Due_DateRequired'),
            'Amount.required' => trans('admin.AmountRequired'),

         ]);



          $del=ExportChecks::find($id);
          $res=Journalizing::where('Code_Type',$del->Code)->where('Type','الشيكات الصادرة')->first();

         $Code=$res->Code;

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','الشيكات الصادرة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','الشيكات الصادرة')->delete();
           Event::where('Type_Code',$del->Code)->where('Type','شيك صادر')->delete();
         $del->delete();



           $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=request('Files');
             }




            $imageImg=request()->file('Image');
          if($imageImg){
            $image_nameImg=Str::random(20);
            $extImg=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameImg=$image_nameImg .'.' . $extImg ;
            $upload_pathImg='SafeTransferFiles/';
            $image_urlImg=$upload_pathImg.$image_full_nameImg;
            $successImg=$imageImg->move($upload_pathImg,$image_full_nameImg);
                   }


             if(!empty($image_urlImg)){

                 $zImage=$image_urlImg;

             }else{
                 $zImage=request('Images');
             }



        $IDD = DB::table('export_checks')->insertGetId(

        array(


            'Code' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Check_Num' => request('Check_Num'),
            'Due_Date' => request('Due_Date'),
            'Amount' => request('Amount'),
            'Status' =>0,
            'Reason' =>null,
            'Check_Type' => request('Check_Type'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Account' => request('Account'),
            'Bank' => request('Bank'),
            'Pay_Account' =>42,
            'Bene_Account' =>null,
            'File' =>$zFile,
             'Image' => $zImage,
            'Signture_Name' => request('Signture_Name'),
            'Bank_Branch' => request('Bank_Branch'),


            'User' =>auth()->guard('admin')->user()->id,

        )
    );



             $c= DB::select("SELECT last_value FROM export_checks_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=request('Code');


                           $event['Start_Date']=request('Due_Date');
         $event['End_Date']=request('Due_Date');
         $event['Event_Ar_Name']='صرف شيك صادر';
         $event['Event_En_Name']='Cashing an exporting check';
         $event['Type']='شيك صادر';
         $event['Type_ID']=null;
         $event['Type_Code']=$CodeT;
         $event['Emp']=null;
         $event['Client']=request('Account');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);


        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
               'Type' => 'الشيكات الصادرة',
            'TypeEn' => 'Exporting Checks',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Account');
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
            $Gen['Type']='الشيكات الصادرة';
        $Gen['TypeEn']= 'Exporting Checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=42;
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
             $Gen['Type']='الشيكات الصادرة';
        $Gen['TypeEn']= 'Exporting Checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=42;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الصادزة';
           $dataUser['ScreenEn']='Exporting Checks';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }

    public function DeleteExportingChecks($id){

        $del=ExportChecks::find($id);

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','الشيكات الصادرة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','الشيكات الصادرة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','رفض الشيكات الصادرة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','دفع شيك صادر')->delete();
          Event::where('Type_Code',$del->Code)->where('Type','شيك صادر')->delete();


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الصادرة';
           $dataUser['ScreenEn']= 'Exporting Checks';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

     public function ReasonExportChecks(){

        ExportChecks::where('id',request('ID'))->update(['Status'=>1,'Reason'=>request('Reason')]);

         $check=ExportChecks::find(request('ID'));

            $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'رفض الشيكات الصادرة',
            'TypeEn' =>  'Refuse Exporting Checks',
            'Code_Type' =>$check->Code,
            'Date' => $check->Date,
            'Draw' => $check->Draw,
            'Coin' => $check->Coin,
            'Cost_Center' => $check->Cost_Center,
            'Total_Debaitor' => $check->Amount,
            'Total_Creditor' => $check->Amount,
            'Note' => $check->Note,

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$check->Amount;
        $PRODUCTSS['Account']=$check->Account;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$check->Code;
        $Gen['Date']=$check->Date;
        $Gen['Type']='رفض الشيكات الصادرة';
        $Gen['TypeEn']='Refuse Exporting Checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$check->Amount;
        $Gen['Statement']=null;
        $Gen['Draw']=$check->Draw;
        $Gen['Debitor_Coin']= $check->Draw *  0;
        $Gen['Creditor_Coin']=$check->Draw * $check->Amount;
        $Gen['Account']=$check->Account;
        $Gen['Coin']= $check->Coin;
        $Gen['Cost_Center']= $check->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$check->Amount;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=42;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$check->Code;
        $Gen['Date']=$check->Date;
        $Gen['Type']='رفض الشيكات الصادرة';
        $Gen['TypeEn']= 'Refuse Exporting Checks';
        $Gen['Debitor']=$check->Amount;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$check->Draw;
        $Gen['Debitor_Coin']= $check->Draw * $check->Amount;
        $Gen['Creditor_Coin']=$check->Draw * 0;
        $Gen['Account']=42;
        $Gen['Coin']= $check->Coin;
        $Gen['Cost_Center']= $check->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الصادرة';
           $dataUser['ScreenEn']='Exporting Checks';
           $dataUser['Type']='رفض شيك';
           $dataUser['TypeEn']='Refuse Checks';
            $dataUser['Explain']=$check->Code;
            $dataUser['ExplainEn']=$check->Code;
           UsersMoves::create($dataUser);

        session()->flash('error',trans('admin.Refused'));
        return back();

           }

    public function TransExportingChecks(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Account'=>'required',
             'Check_Type'=>'required',
             'Bank'=>'required',
             'Check_Num'=>'required',
             'Due_Date'=>'required',
             'Amount'=>'required',

               ],[

            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),
            'Account.required' => trans('admin.AccountRequired'),
            'Check_Type.required' => trans('admin.Check_TypeRequired'),
            'Bank.required' => trans('admin.BankRequired'),
            'Check_Num.required' => trans('admin.Check_NumRequired'),
            'Due_Date.required' => trans('admin.Due_DateRequired'),
            'Amount.required' => trans('admin.AmountRequired'),

         ]);


          ExportChecks::where('id',request('ID'))->update(['Status'=>2,'Bene_Account'=>request('Bene_Account')]);

              $ress=IncomChecks::orderBy('id','desc')->first();

           if(!empty($ress->Code)){

              $Codee=$ress->Code + 1 ;
           }else{
              $Codee=1;

           }

        $IDD = DB::table('incom_checks')->insertGetId(

        array(


            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Check_Num' => request('Check_Num'),
            'Due_Date' => request('Due_Date'),
            'Amount' => request('Amount'),
            'Status' =>0,
            'Reason' =>null,
            'Check_Type' => request('Check_Type'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Account' => request('Bene_Account'),
            'Bank' => request('Bank'),
            'Arrest_Account' =>35,
            'Bene_Account' =>null,
            'User' =>auth()->guard('admin')->user()->id,

        )
    );



             $c= DB::select("SELECT last_value FROM incom_checks_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;



           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' =>'الشيكات الواردة',
            'TypeEn' =>'Incoming checks',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Bene_Account');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الواردة';
        $Gen['TypeEn']='Incoming checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Bene_Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=35;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الواردة';
        $Gen['TypeEn']='Incoming checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=35;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='الشيكات الواردة';
           $dataUser['ScreenEn']='Incoming checks';
           $dataUser['Type']='محوله';
           $dataUser['TypeEn']='Transfered';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Transfer_Successfully'));
             return back();

    }

     public function PayExportingChecks(){


           ExportChecks::where('id',request('ID'))->update(['Status'=>3]);

           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'دفع شيك صادر',
            'TypeEn' => 'Pay Exporting Checks',
            'Code_Type' => request('Code'),
            'Date' => date('Y-m-d'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=42;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='دفع شيك صادر';
        $Gen['TypeEn']='Pay Exporting Checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=42;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('BankSafe_Account');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='دفع شيك صادر';
        $Gen['TypeEn']='Pay Exporting Checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('BankSafe_Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الصادرة';
           $dataUser['ScreenEn']='Exporting Checks';
           $dataUser['Type']='دفع شيك';
           $dataUser['TypeEn']='Check Payed';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Check_Payed'));
             return back();

    }

     public function PrintOutcomChecks($id){

          $item=ExportChecks::find($id);

         return view('admin.Sechdules.PrintOutcomChecks',[
             'item'=>$item,


         ]);
    }

     public function PrintIncomChecks($id){

          $item=IncomChecks::find($id);

         return view('admin.Sechdules.PrintIncomChecks',[
             'item'=>$item,


         ]);
    }


         // ====  Incoming Checks  ===
     public function Incoming_checksPage(){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $ChecksTypes=ChecksTypes::all();
          $items=IncomChecks::paginate(50);

          $res=IncomChecks::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

         return view('admin.Accounts.IncomChecks',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'ChecksTypes'=>$ChecksTypes,
             'items'=>$items,

         ]);
    }
         public function IncomingchecksSechduleFilter(){


              $from=request('From');
             $to=request('To');
             $CodeType=request('Code');
             $Account=request('Account');
             $Due_Date=request('Due_Date');
                    $items=IncomChecks::whereBetween('Date',[$from,$to])

                                  ->when(!empty($CodeType), function ($query) use ($CodeType) {
        return $query->where('Code',$CodeType);

                })

                                  ->when(!empty($Account), function ($query) use ($Account) {
        return $query->where('Account',$Account);

                })

                                  ->when(!empty($Due_Date), function ($query) use ($Due_Date) {
        return $query->where('Due_Date',$Due_Date);

                })

          ->paginate(50);

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();
          $ChecksTypes=ChecksTypes::all();


   $res=IncomChecks::orderBy('id','desc')->first();
           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }
             $items->appends(request()->query());
         return view('admin.Accounts.IncomChecks',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'ChecksTypes'=>$ChecksTypes,
             'items'=>$items,

         ]);
    }

     public function AddIncoming_checks(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',

             'Account'=>'required',
             'Check_Type'=>'required',
             'Bank'=>'required',
             'Check_Num'=>'required',
             'Due_Date'=>'required',
             'Amount'=>'required',

               ],[

            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),
            'Account.required' => trans('admin.AccountRequired'),
            'Check_Type.required' => trans('admin.Check_TypeRequired'),
            'Bank.required' => trans('admin.BankRequired'),
            'Check_Num.required' => trans('admin.Check_NumRequired'),
            'Due_Date.required' => trans('admin.Due_DateRequired'),
            'Amount.required' => trans('admin.AmountRequired'),

         ]);


          $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=null;
             }



   $imageImg=request()->file('Image');
          if($imageImg){
            $image_nameImg=Str::random(20);
            $extImg=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameImg=$image_nameImg .'.' . $extImg ;
            $upload_pathImg='SafeTransferFiles/';
            $image_urlImg=$upload_pathImg.$image_full_nameImg;
            $successImg=$imageImg->move($upload_pathImg,$image_full_nameImg);
                   }


             if(!empty($image_urlImg)){

                 $zImage=$image_urlImg;

             }else{
                 $zImage=null;
             }

        $IDD = DB::table('incom_checks')->insertGetId(

        array(


            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Check_Num' => request('Check_Num'),
            'Due_Date' => request('Due_Date'),
            'Amount' => request('Amount'),
            'Status' =>0,
            'Reason' =>null,
            'Check_Type' => request('Check_Type'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Account' => request('Account'),
            'Bank' => request('Bank'),
            'Arrest_Account' =>35,
            'Bene_Account' =>null,
            'File' =>$zFile,
             'Image' => $zImage,
            'Signture_Name' => request('Signture_Name'),
            'Bank_Branch' => request('Bank_Branch'),


            'User' =>auth()->guard('admin')->user()->id,

        )
    );



             $c= DB::select("SELECT last_value FROM incom_checks_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;


         $event['Start_Date']=request('Due_Date');
         $event['End_Date']=request('Due_Date');
         $event['Event_Ar_Name']='صرف شيك وارد';
         $event['Event_En_Name']='Cashing an incoming check';
         $event['Type']='شيك وارد';
         $event['Type_ID']=null;
         $event['Type_Code']=$CodeT;
         $event['Emp']=null;
         $event['Client']=request('Account');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);


           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'الشيكات الواردة',
            'TypeEn' => 'Incoming checks',
            'Code_Type' =>$CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Account');
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الواردة';
        $Gen['TypeEn']='Incoming checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=35;
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الواردة';
        $Gen['TypeEn']='Incoming checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=35;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الواردة';
           $dataUser['ScreenEn']='Incoming checks';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditIncoming_checks($id){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',

             'Account'=>'required',
             'Check_Type'=>'required',
             'Bank'=>'required',
             'Check_Num'=>'required',
             'Due_Date'=>'required',
             'Amount'=>'required',

               ],[

            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),
            'Account.required' => trans('admin.AccountRequired'),
            'Check_Type.required' => trans('admin.Check_TypeRequired'),
            'Bank.required' => trans('admin.BankRequired'),
            'Check_Num.required' => trans('admin.Check_NumRequired'),
            'Due_Date.required' => trans('admin.Due_DateRequired'),
            'Amount.required' => trans('admin.AmountRequired'),

         ]);


                $del=IncomChecks::find($id);
          $res=Journalizing::where('Code_Type',$del->Code)->where('Type','الشيكات الواردة')->first();

         $Code=$res->Code;

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','الشيكات الواردة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','الشيكات الواردة')->delete();
         Event::where('Type_Code',$del->Code)->where('Type','شيك وارد')->delete();
         $del->delete();

          $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=request('Files');
             }




   $imageImg=request()->file('Image');
          if($imageImg){
            $image_nameImg=Str::random(20);
            $extImg=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameImg=$image_nameImg .'.' . $extImg ;
            $upload_pathImg='SafeTransferFiles/';
            $image_urlImg=$upload_pathImg.$image_full_nameImg;
            $successImg=$imageImg->move($upload_pathImg,$image_full_nameImg);
                   }


             if(!empty($image_urlImg)){

                 $zImage=$image_urlImg;

             }else{
                 $zImage=request('Images');
             }


        $IDD = DB::table('incom_checks')->insertGetId(

        array(


            'Code' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Check_Num' => request('Check_Num'),
            'Due_Date' => request('Due_Date'),
            'Amount' => request('Amount'),
            'Status' =>0,
            'Reason' =>null,
            'Check_Type' => request('Check_Type'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Account' => request('Account'),
            'Bank' => request('Bank'),
            'Arrest_Account' =>35,
            'Bene_Account' =>null,
            'File' =>$zFile,
             'Image' => $zImage,
            'Signture_Name' => request('Signture_Name'),
            'Bank_Branch' => request('Bank_Branch'),

            'User' =>auth()->guard('admin')->user()->id,

        )
    );



                 $c= DB::select("SELECT last_value FROM incom_checks_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=request('Code');


                  $event['Start_Date']=request('Due_Date');
         $event['End_Date']=request('Due_Date');
         $event['Event_Ar_Name']='صرف شيك وارد';
         $event['Event_En_Name']='Cashing an incoming check';
         $event['Type']='شيك وارد';
         $event['Type_ID']=null;
         $event['Type_Code']=$CodeT;
         $event['Emp']=null;
         $event['Client']=request('Account');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' =>'الشيكات الواردة',
            'TypeEn' => 'Incoming checks',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Account');
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الواردة';
        $Gen['TypeEn']='Incoming checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=35;
        $PRODUCTSS['Statement']=request('Note');


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الواردة';
        $Gen['TypeEn']='Incoming checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=request('Note');
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=35;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الواردة';
           $dataUser['ScreenEn']='Incoming checks';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Update'));
             return back();

    }

    public function DeleteIncoming_checks($id){

        $del=IncomChecks::find($id);

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','الشيكات الواردة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','الشيكات الواردة')->delete();
                  Journalizing::where('Code_Type',$del->Code)->where('Type','رفض الشيكات الواردة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','دفع شيك وارد')->delete();

           Event::where('Type_Code',$del->Code)->where('Type','شيك وارد')->delete();

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.Incoming_checks');
           $dataUser['Type']=trans('admin.Delete');
            $dataUser['Explain']=$del->Code;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

     public function ReasonIncoming_checks(){

        IncomChecks::where('id',request('ID'))->update(['Status'=>1,'Reason'=>request('Reason')]);


          $check=IncomChecks::find(request('ID'));

            $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'رفض الشيكات الواردة',
            'TypeEn' =>'Refuse Incoming checks',
            'Code_Type' =>$check->Code,
            'Date' => $check->Date,
            'Draw' => $check->Draw,
            'Coin' => $check->Coin,
            'Cost_Center' => $check->Cost_Center,
            'Total_Debaitor' => $check->Amount,
            'Total_Creditor' => $check->Amount,
            'Note' => $check->Note,

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$check->Amount;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$check->Account;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$check->Code;
        $Gen['Date']=$check->Date;
        $Gen['Type']='رفض الشيكات الواردة';
        $Gen['TypeEn']='Refuse Incoming checks';
        $Gen['Debitor']=$check->Amount;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$check->Draw;
        $Gen['Debitor_Coin']= $check->Draw *  $check->Amount;
        $Gen['Creditor_Coin']=$check->Draw * 0;
        $Gen['Account']=$check->Account;
        $Gen['Coin']= $check->Coin;
        $Gen['Cost_Center']= $check->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$check->Amount;
        $PRODUCTSS['Account']=35;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$check->Code;
        $Gen['Date']=$check->Date;
        $Gen['Type']='رفض الشيكات الواردة';
        $Gen['TypeEn']='Refuse Incoming checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$check->Amount;
        $Gen['Statement']=null;
        $Gen['Draw']=$check->Draw;
        $Gen['Debitor_Coin']= $check->Draw * 0;
        $Gen['Creditor_Coin']=$check->Draw * $check->Amount;
        $Gen['Account']=35;
        $Gen['Coin']= $check->Coin;
        $Gen['Cost_Center']= $check->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الواردة';
           $dataUser['ScreenEn']='Incoming checks';
           $dataUser['Type']='رفض شيك';
           $dataUser['TypeEn']='Refuse Checks';
            $dataUser['Explain']=$check->Code;
            $dataUser['ExplainEn']=$check->Code;
           UsersMoves::create($dataUser);

        session()->flash('error',trans('admin.Refused'));
        return back();

           }

    public function TransIncoming_checks(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Account'=>'required',
             'Check_Type'=>'required',
             'Bank'=>'required',
             'Check_Num'=>'required',
             'Due_Date'=>'required',
             'Amount'=>'required',

               ],[

            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),
            'Account.required' => trans('admin.AccountRequired'),
            'Check_Type.required' => trans('admin.Check_TypeRequired'),
            'Bank.required' => trans('admin.BankRequired'),
            'Check_Num.required' => trans('admin.Check_NumRequired'),
            'Due_Date.required' => trans('admin.Due_DateRequired'),
            'Amount.required' => trans('admin.AmountRequired'),

         ]);



          IncomChecks::where('id',request('ID'))->update(['Status'=>2,'Bene_Account'=>request('Bene_Account')]);

              $ress=ExportChecks::orderBy('id','desc')->first();

           if(!empty($ress->Code)){

              $Codee=$ress->Code + 1 ;
           }else{
              $Codee=1;

           }

        $IDD = DB::table('export_checks')->insertGetId(

        array(


            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Note' => request('Note'),
            'Check_Num' => request('Check_Num'),
            'Due_Date' => request('Due_Date'),
            'Amount' => request('Amount'),
            'Status' =>0,
            'Reason' =>null,
            'Check_Type' => request('Check_Type'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Account' => request('Bene_Account'),
            'Bank' => request('Bank'),
            'Pay_Account' =>42,
            'Bene_Account' =>null,
            'User' =>auth()->guard('admin')->user()->id,

        )
    );




             $c= DB::select("SELECT last_value FROM export_checks_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;


           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'الشيكات الصادرة',
            'TypeEn' => 'Exporting Checks',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Bene_Account');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الصادرة';
        $Gen['TypeEn']='Exporting Checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Bene_Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=42;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']='الشيكات الصادرة';
        $Gen['TypeEn']='Exporting Checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=42;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الشيكات الصادرة';
           $dataUser['ScreenEn']='Exporting Checks';
           $dataUser['Type']='محوله';
           $dataUser['TypeEn']='Transfered';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Transfer_Successfully'));
             return back();

    }

      public function PayIncomingChecks(){


           IncomChecks::where('id',request('ID'))->update(['Status'=>3]);

           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'دفع شيك وارد',
            'TypeEn' => 'Pay Incoming Checks',
            'Code_Type' => request('Code'),
            'Date' => date('Y-m-d'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=35;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='دفع شيك وارد';
        $Gen['TypeEn']='Pay Incoming Checks';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=35;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('BankSafe_Account');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='دفع شيك وارد';
        $Gen['TypeEn']='Pay Incoming Checks';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('BankSafe_Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());


                     $dataUser['Screen']='الشيكات الواردة';
           $dataUser['ScreenEn']='Incoming checks';
           $dataUser['Type']='دفع شيك';
           $dataUser['TypeEn']='Check Payed';
            $dataUser['Explain']=request('Code');
            $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Check_Payed'));
             return back();

    }


    // ===   Insurance Paper ====
     public function Insurance_PaperPage(){

          $CostCenters=CostCenter::all();
          $Coins=Coins::all();

          $items=InsurancePaper::with(['Account', 'Coin', 'Cost_Center', 'Bank', 'User'])->paginate(50);

          $res=InsurancePaper::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

         return view('admin.Accounts.InsurancePaper',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'items'=>$items,

         ]);
    }

      public function InsurancePaperSechduleFilter(){



           $from=request('From');
             $to=request('To');
             $CodeType=request('Code');
             $Account=request('Account');
             $Due_Date=request('Due_Date');




                    $items=InsurancePaper::with(['Account', 'Coin', 'Cost_Center', 'Bank', 'User'])->whereBetween('Date',[$from,$to])

                                  ->when(!empty($CodeType), function ($query) use ($CodeType) {
        return $query->where('Code',$CodeType);

                })

                                  ->when(!empty($Account), function ($query) use ($Account) {
        return $query->where('Account',$Account);

                })

                                  ->when(!empty($Due_Date), function ($query) use ($Due_Date) {
        return $query->where('Due_Date',$Due_Date);

                })

          ->paginate(50);


          $CostCenters=CostCenter::all();
          $Coins=Coins::all();



          $res=InsurancePaper::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }
             $items->appends(request()->query());
         return view('admin.Accounts.InsurancePaper',[
             'CostCenters'=>$CostCenters,
             'Coins'=>$Coins,
             'Code'=>$Code,
             'items'=>$items,

         ]);
    }

       public function AddInsurancePaper(){

        $data= $this->validate(request(),[
             'Date'=>'required',
             'Coin'=>'required',
             'Draw'=>'required',
             'Account'=>'required',
             'Due_Date'=>'required',
             'Amount'=>'required',

               ],[

            'Date.required' => trans('admin.DateRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Cost_Center.required' => trans('admin.Cost_CenterRequired'),
            'Account.required' => trans('admin.AccountRequired'),
            'Check_Type.required' => trans('admin.Check_TypeRequired'),
            'Bank.required' => trans('admin.BankRequired'),
            'Check_Num.required' => trans('admin.Check_NumRequired'),
            'Due_Date.required' => trans('admin.Due_DateRequired'),
            'Amount.required' => trans('admin.AmountRequired'),

         ]);


              $imageFile=request()->file('File');
          if($imageFile){
            $image_nameFile=Str::random(20);
            $extFile=strtolower($imageFile->getClientOriginalExtension());
            $image_full_nameFile=$image_nameFile .'.' . $extFile ;
            $upload_pathFile='SafeTransferFiles/';
            $image_urlFile=$upload_pathFile.$image_full_nameFile;
            $successFile=$imageFile->move($upload_pathFile,$image_full_nameFile);
                   }


             if(!empty($image_urlFile)){

                 $zFile=$image_urlFile;

             }else{
                 $zFile=null;
             }



                if(!empty(request('FromEn'))){
         $FromEEN=request('FromEn');

          }else{
      $FromEEN=request('From');
          }


                        if(!empty(request('ToEn'))){
         $ToEEN=request('ToEn');

          }else{
      $ToEEN=request('To');
          }


        $IDD = DB::table('insurance_papers')->insertGetId(

        array(


            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'From' => request('From'),
            'To' => request('To'),
            'FromEn' => $FromEEN,
            'ToEn' => $ToEEN,
            'Note' => request('Note'),
            'Due_Date' => request('Due_Date'),
            'Amount' =>request('Amount'),
            'Status' =>0,
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Account' => request('Account'),
            'Bank' => null,
            'File' => $zFile,
            'User' => auth()->guard('admin')->user()->id,


        )
    );




               $c= DB::select("SELECT last_value FROM insurance_papers_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;

                  $event['Start_Date']=request('Due_Date');
         $event['End_Date']=request('Due_Date');
         $event['Event_Ar_Name']='وصل امانة';
         $event['Event_En_Name']='Insurance Paper';
         $event['Type']='وصل امانة';
         $event['Type_ID']=null;
         $event['Type_Code']=$CodeT;
         $event['Emp']=null;
         $event['Client']=request('Account');
         $event['Product']=null;
         $event['Customer']=null;
         Event::create($event);



           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'وصل أمانة',
            'TypeEn' => 'Insurance Paper',
            'Code_Type' => $CodeT,
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('Account');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']= 'وصل أمانة';
        $Gen['TypeEn']='Insurance Paper';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('Account');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=35;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=request('Date');
        $Gen['Type']= 'وصل أمانة';
        $Gen['TypeEn']='Insurance Paper';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=35;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']= 'وصل أمانة';
           $dataUser['ScreenEn']='Insurance Paper';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=$CodeT;
           $dataUser['ExplainEn']=$CodeT;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

      public function DeleteInsurancePaper($id){

        $del=InsurancePaper::find($id);

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','وصل أمانة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','وصل أمانة')->delete();
            Event::where('Type_Code',$del->Code)->where('Type','وصل امانة')->delete();
           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']= 'وصل أمانة';
           $dataUser['ScreenEn']='Insurance Paper';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Code;
            $dataUser['ExplainEn']=$del->Code;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

        public function RecivedInurance($id){

            $item=InsurancePaper::find($id);

         InsurancePaper::where('id',$item->id)->update(['Status'=>1,'Bank'=>request('Bank')]);

           $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'استلام وصل امانه',
            'TypeEn' => 'Insurance Paper Recived',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $item->Amount,
            'Total_Creditor' => $item->Amount,
            'Note' => $item->Note,

        )
    );



        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']= $item->Amount;
        $PRODUCTSS['Account']=35;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='استلام وصل امانه';
        $Gen['TypeEn']='Insurance Paper Recived';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$item->Amount;
        $Gen['Statement']=null;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * 0;
        $Gen['Creditor_Coin']=$item->Draw * $item->Amount;
        $Gen['Account']=35;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$item->Amount;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Bank');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='استلام وصل امانه';
        $Gen['TypeEn']='Insurance Paper Recived';
        $Gen['Debitor']=$item->Amount;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $item->Amount;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=request('Bank');
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;



         GeneralDaily::create($Gen);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='استلام وصل امانه';
           $dataUser['ScreenEn']='Insurance Paper Recived';
           $dataUser['Type']='استلام وصل امانه';
           $dataUser['TypeEn']='Insurance Paper Recived';
           $dataUser['Explain']=$item->Code;
           $dataUser['ExplainEn']=$item->Code;
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Insurance_Paper_Recived'));
             return back();

    }

    //====== Safes Banks =======
      public function Safes_BanksPage(){
        $items=SafesBanks::all();
        $Branches=Branches::all();

            $res=SafesBanks::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }


         return view('admin.Accounts.SafesBanks',['items'=>$items,'Code'=>$Code,'Branches'=>$Branches]);
    }

      public function AddSafes_Banks(){

        $data= $this->validate(request(),[
             'Name'=>'required',
             'Type'=>'required',



               ],[
            'Name.required' => trans('admin.NameRequired'),
            'Type.required' => trans('admin.TypeRequired'),


         ]);



          if(request('Type') == 1){

              $ty=28;
          }else{

             $ty=29;
          }


            $count=AcccountingManual::orderBy('id','desc')->where('Parent',$ty)->count();
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',$ty)->first();
            $codee=AcccountingManual::find($ty);

                if($count == 0){

                $x=$codee->Code.'01';
             $data['Code']=(int) $x ;

                }else{

             $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;
            }
                $x= $codee->Code.$NewXY;
                  $data['Code']=(int) $x;

                }

         $data['Name']=request('Name');
            if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
        $data['NameEn']=request('Name');
          }
         $data['Type']=1;
         $data['Parent']=$ty;
         $data['Note']=request('Note');

         $data['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($data);


         $Acc=AcccountingManual::orderBy('id','desc')->first();


        $dataa['Code']=request('Code');
         $dataa['Date']=date('Y-m-d');
         $dataa['Name']=request('Name');
              if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
        $dataa['NameEn']=request('Name');
          }
         $dataa['Type']=request('Type');
         $dataa['Note']=request('Note');
         $dataa['Service_Fee']=request('Service_Fee');
         $dataa['Account']=$Acc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;
             $dataa['Branch']=request('Branch');
         SafesBanks::create($dataa);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الخزائن و البنوك';
           $dataUser['ScreenEn']='Safes and Banks';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Name');

              if(!empty(request('NameEn'))){
          $dataUser['ExplainEn']=request('NameEn');
          }else{
        $dataUser['ExplainEn']=request('Name');

          }
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

       public function EditSafes_Banks($id){

        $data= $this->validate(request(),[
             'Name'=>'required',
             'Type'=>'required',



               ],[
            'Name.required' => trans('admin.NameRequired'),
            'Type.required' => trans('admin.TypeRequired'),


         ]);


     $safe=SafesBanks::find($id);


         AcccountingManual::where('id',$safe->Account)->update(['Name'=>request('Name'),'NameEn'=>request('NameEn'),'Note'=>request('Note')]);


         $Acc=AcccountingManual::orderBy('id','desc')->first();


        $dataa['Code']=request('Code');
         $dataa['Date']=request('Date');
         $dataa['Name']=request('Name');
         $dataa['NameEn']=request('NameEn');
         $dataa['Type']=request('Type');
         $dataa['Note']=request('Note');
         $dataa['Account']=request('Account');
         $dataa['User']=request('User');
         $dataa['Service_Fee']=request('Service_Fee');
         $dataa['Branch']=request('Branch');

         SafesBanks::where('id',$id)->update($dataa);


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
               $dataUser['Screen']='الخزائن و البنوك';
           $dataUser['ScreenEn']='Safes and Banks';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Name');
           $dataUser['ExplainEn']=request('NameEn');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }

        public function DeleteSafes_Banks($id){

        $del=SafesBanks::find($id);
         AcccountingManual::where('id',$del->Account)->delete();




           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                       $dataUser['Screen']='الخزائن و البنوك';
           $dataUser['ScreenEn']='Safes and Banks';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Name;
            $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


    //===== Safes Transfers ===
     public function SafesTransferPage(){
        $items=SafeTransfers::all();
            $Coins=Coins::all();
          $CostCenters=CostCenter::all();
            $res=SafeTransfers::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

          $Safes = AcccountingManual::
          where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();

            $Emps = Employess::orderBy('id','asc')
              ->where('EmpSort',1)->where('Active',1)
              ->get();
               $Branches=Branches::all();
         return view('admin.Accounts.SafesTransfer',[
             'items'=>$items,
             'Code'=>$Code,
             'Coins'=>$Coins,
             'CostCenters'=>$CostCenters,
             'Safes'=>$Safes,
             'Emps'=>$Emps,
             'Branches'=>$Branches,
         ]);
    }

     public function AddSafeTransfer(){

        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Draw'=>'required',
             'Amount'=>'required',
             'From_Safe'=>'required',
             'To_Safe'=>'required',
             'Coin'=>'required',
               ],[
            'Code.required' => trans('admin.CodeRequired'),
            'Date.required' => trans('admin.DateRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Amount.required' => trans('admin.AmountRequired'),
            'From_Safe.required' => trans('admin.From_SafeRequired'),
            'To_Safe.required' => trans('admin.To_SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
         ]);

           $image=request()->file('File');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='SafeTransferFiles/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['File']=$image_url;

             }else{
                 $data['File']=null;
             }
         $data['Delegate']=request('Delegate');
         $data['Time']=date("h:i:s a", time());
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Draw']=request('Draw');
         $data['Amount']=request('Amount');
         $data['Note']=request('Note');
         $data['From_Safe']=request('From_Safe');
         $data['Branch']=request('Branch');
         $data['To_Safe']=request('To_Safe');
         $data['Coin']=request('Coin');
         $data['Status']=0;
         $data['Edit']=0;
         $data['OldAmount']=request('Amount');
         $data['Cost_Center']=request('Cost_Center');
         $data['User']=auth()->guard('admin')->user()->id;

         SafeTransfers::create($data);

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='تحويل خزائن يحتاج للتأكيد';
         $notii['Noti_En_Name']='Safe Transfer Need Confirmation';
         $notii['Type']='تحويلات خزائن';
         $notii['TypeEn']='Safe Transfers';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=request('Delegate');
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=request('To_Safe');
         Notifications::create($notii);

   notify()->warning(trans('admin.Safe_Transfer_Need_Confirmation'));




           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='تحويلات خزائن';
           $dataUser['ScreenEn']='Safes Transfer';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function TransferSureSafe($id){
        $items=SafeTransfers::all();
            $Coins=Coins::all();
          $CostCenters=CostCenter::all();

            $item=SafeTransfers::find($id);
                $Emps = Employess::orderBy('id','asc')
              ->where('EmpSort',1)->where('Active',1)
              ->get();
             $Branches=Branches::all();

           $item=SafeTransfers::find($id);
         if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('SafesTransferSechdule');

                    }


         return view('admin.Accounts.SureSafesTransfer',[
             'items'=>$items,
             'item'=>$item,
             'Coins'=>$Coins,
             'CostCenters'=>$CostCenters,
             'Emps'=>$Emps,
             'Branches'=>$Branches,
         ]);
    }

        public function SureSafeTransfer($id){

        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Draw'=>'required',
             'Amount'=>'required',
             'From_Safe'=>'required',
             'To_Safe'=>'required',
             'Coin'=>'required',
               ],[
            'Code.required' => trans('admin.CodeRequired'),
            'Date.required' => trans('admin.DateRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Amount.required' => trans('admin.AmountRequired'),
            'From_Safe.required' => trans('admin.From_SafeRequired'),
            'To_Safe.required' => trans('admin.To_SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
         ]);


              $item=SafeTransfers::find($id);
         if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('SafesTransferSechdule');

                    }


                    $image=request()->file('File');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='SafeTransferFiles/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['File']=$image_url;

             }else{
                 $data['File']=request('Files');
             }

                 $data['Delegate']=request('Delegate');

         $data['Status']=1 + request('Edit');
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Draw']=request('Draw');
         $data['Amount']=request('Amount');
         $data['Note']=request('Note');
         $data['From_Safe']=request('From_Safe');
         $data['To_Safe']=request('To_Safe');
         $data['Coin']=request('Coin');
         $data['OldAmount']=request('OldAmount');
         $data['Cost_Center']=request('Cost_Center');
         $data['User']=auth()->guard('admin')->user()->id;
  $data['Branch']=request('Branch');
         SafeTransfers::where('id',$id)->update($data);





             $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }


         $JunID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'تحويلات الخزائن',
            'TypeEn' =>'Safes Transfer',
            'Code_Type' => request('Code'),
            'Date' => request('Date'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );


    $store=Stores::find(request('Store'));

        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('To_Safe');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=request('Date');
        $Gen['Type']='تحويلات الخزائن';
        $Gen['TypeEn']='Safes Transfer';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('To_Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']=request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Amount');
        $PRODUCTSS['Account']=request('From_Safe');
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=request('Date');
        $Gen['Type']='تحويلات الخزائن';
        $Gen['TypeEn']='Safes Transfer';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Amount');
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * request('Amount');
        $Gen['Account']=request('From_Safe');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);

                  $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='تأكيد تحويل خزينة';
         $notii['Noti_En_Name']='Confirm Safe Transfer';
         $notii['Type']='تحويلات خزائن';
          $notii['TypeEn']='Safe Transfers';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=request('Delegate');
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=request('To_Safe');
         Notifications::create($notii);

                notify()->success(trans('admin.ConfirmationSafeTransfer'));

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='تحويلات خزائن';
           $dataUser['ScreenEn']='Safes Transfer';
           $dataUser['Type']='تأكيد';
           $dataUser['TypeEn']='Sure';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return redirect('SafesTransferSechdule');

    }

     public function SafesTransferSechdulePage(){

           if(auth()->guard('admin')->user()->emp == 0){

    $items=SafeTransfers::paginate(100);
     }else{

            if(!empty(auth()->guard('admin')->user()->safe)){
      $items=SafeTransfers::where('To_Safe',auth()->guard('admin')->user()->safe)
                ->paginate(100);
            }else{

           $items=SafeTransfers::paginate(100);
            }

     }

         return view('admin.Accounts.SafesTransferSechdule',['items'=>$items]);
    }

 public function TransSafePrint($id){

          $item=SafeTransfers::find($id);
         return view('admin.Sechdules.SafeTransferPrint',[
             'item'=>$item,
         ]);
    }

         public function EditSafeTransfer($id){
        $items=SafeTransfers::all();
            $Coins=Coins::all();
          $CostCenters=CostCenter::all();

            $item=SafeTransfers::find($id);
                    $Emps = Employess::orderBy('id','asc')
              ->where('EmpSort',1)->where('Active',1)
              ->get();
             $Branches=Branches::all();



         if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('SafesTransferSechdule');

                    }

         return view('admin.Accounts.EditSafesTransfer',[
             'items'=>$items,
             'item'=>$item,
             'Coins'=>$Coins,
             'CostCenters'=>$CostCenters,
             'Emps'=>$Emps,
             'Branches'=>$Branches,
         ]);
    }

         public function RefusedSafeTransfer($id){

        SafeTransfers::where('id',$id)->update(['Status'=>2]);

                          $del=SafeTransfers::find($id);

                          $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='رفض تحويل خزينة';
         $notii['Noti_En_Name']='Refuse Safe Transfer';
         $notii['Type']='تحويلات خزائن';
              $notii['TypeEn']='Safe Transfers';
         $notii['Type_Code']=$del->Code;
         $notii['Emp']=$del->Delegate;
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=$del->To_Safe;
         Notifications::create($notii);

                notify()->error(trans('admin.RefuseSafeTransfer'));


        session()->flash('error',trans('admin.Refused'));
        return back();

           }

         public function PostEditSafeTransfer($id){

        $data= $this->validate(request(),[
             'Code'=>'required',
             'Date'=>'required',
             'Draw'=>'required',
             'Amount'=>'required',
             'From_Safe'=>'required',
             'To_Safe'=>'required',
             'Coin'=>'required',
               ],[
            'Code.required' => trans('admin.CodeRequired'),
            'Date.required' => trans('admin.DateRequired'),
            'Draw.required' => trans('admin.DrawRequired'),
            'Amount.required' => trans('admin.AmountRequired'),
            'From_Safe.required' => trans('admin.From_SafeRequired'),
            'To_Safe.required' => trans('admin.To_SafeRequired'),
            'Coin.required' => trans('admin.CoinRequired'),
         ]);


               $item=SafeTransfers::find($id);
         if($item->Status != 0){
   session()->flash('error',trans('admin.Already_Done'));
               return redirect('SafesTransferSechdule');

                    }

                    $image=request()->file('File');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='SafeTransferFiles/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['File']=$image_url;

             }else{
                 $data['File']=request('Files');
             }

              $data['Delegate']=request('Delegate');

         $data['Status']=0;
         $data['Code']=request('Code');
         $data['Date']=request('Date');
         $data['Draw']=request('Draw');
         $data['Amount']=request('Amount');
         $data['Note']=request('Note');
         $data['From_Safe']=request('From_Safe');
         $data['To_Safe']=request('To_Safe');
         $data['Coin']=request('Coin');
         $data['OldAmount']=request('OldAmount');
         $data['Edit']=1;
         $data['Cost_Center']=request('Cost_Center');
         $data['User']=auth()->guard('admin')->user()->id;
  $data['Branch']=request('Branch');
         SafeTransfers::where('id',$id)->update($data);

             $del=SafeTransfers::find($id);
           Notifications::where('Type_Code',$del->Code)->where('Type','تحويلات خزائن')->delete();

         $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='تحويل خزائن يحتاج للتأكيد';
         $notii['Noti_En_Name']='Safe Transfer Need Confirmation';
         $notii['Type']='تحويلات خزائن';
         $notii['Type_Code']=request('Code');
         $notii['Emp']=request('Delegate');
         $notii['Client']=null;
         $notii['Product']=null;
         $notii['Store']=null;
         $notii['Safe']=request('To_Safe');
         Notifications::create($notii);

                notify()->success(trans('admin.Safe_Transfer_Need_Confirmation'));




           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
          $dataUser['Screen']='تحويلات خزائن';
           $dataUser['ScreenEn']='Safes Transfer';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Code');
           $dataUser['ExplainEn']=request('Code');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Updated'));
             return redirect('SafesTransferSechdule');

    }



      //===== Assets ===
              public function AssetsPage(){
        $items=Assets::paginate(100);

            $res=Assets::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

                    $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();


        $Ehlak = AcccountingManual::
             where('Type',1)
              ->where('Parent',120)
              ->get();

            $CostCenters=CostCenter::all();
          $Coins=Coins::all();
               $Branchs=Branches::all();

             $itemsX=Assets::where('Asset_Net','>',1)->where('Asset_Type','مستهلك')->get();
                  $x1=date('Y')."-01";
                  $x2=date('Y')."-02";
                  $x3=date('Y')."-03";
                  $x4=date('Y')."-04";
                  $x5=date('Y')."-05";
                  $x6=date('Y')."-06";
                  $x7=date('Y')."-07";
                  $x8=date('Y')."-08";
                  $x9=date('Y')."-09";
                  $x10=date('Y')."-10";
                  $x11=date('Y')."-11";
                  $x12=date('Y')."-12";
                 foreach($itemsX as $item){

                if(!empty($item->Depreciation_Complex)){
                       if(!empty($item->Ehlak)){

  $date = new DateTime($item->Purchases_Date);
$date->modify('-1 month');
$CD=$date->format('Y-m');

                if($CD != date('Y-m')){

                     if($item->Purchases_Date == $x1){
                     if($item->M1 != $x1){

                    if($item->Asset_Type == 'مستهلك'){


                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }

                      Assets::where('id',$item->id)->update(['M1'=>$x1]);
                     }
                     }

                      if($item->Purchases_Date == $x2){
                           if($item->M2 != $x2){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                     Assets::where('id',$item->id)->update(['M2'=>$x2]);

                     }
                     }

                      if($item->Purchases_Date == $x3){
                           if($item->M3 != $x3){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M3'=>$x3]);
                     }
                     }

                      if($item->Purchases_Date == $x4){
                           if($item->M4 != $x4){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);


               if(is_numeric($item->Asset_Net)){
               $ff['Asset_Net']=$item->Asset_Net - $res ;
               }else{
                 $ff['Asset_Net']=0 - $res ;
               }
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M4'=>$x4]);
                     }
                     }

                      if($item->Purchases_Date == $x5){
                           if($item->M5 != $x5){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M5'=>$x5]);
                     }
                     }

                      if($item->Purchases_Date == $x6){
                           if($item->M6 != $x6){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M6'=>$x6]);
                     }
                     }

                      if($item->Purchases_Date == $x7){
                           if($item->M7 != $x7){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' => 'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M7'=>$x7]);
                     }
                     }


                      if($item->Purchases_Date == $x8){
                           if($item->M8 != $x8){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' =>'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M8'=>$x8]);
                     }
                     }


                      if($item->Purchases_Date == $x9){
                           if($item->M9 != $x9){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M9'=>$x9]);
                     }
                     }


                      if($item->Purchases_Date == $x10){
                           if($item->M10 != $x10){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' =>'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M10'=>$x10]);
                     }
                     }


                      if($item->Purchases_Date == $x11){
                           if($item->M11 != $x11){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M11'=>$x11]);
                     }
                     }

                      if($item->Purchases_Date == $x12){
                           if($item->M12 != $x12){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => 'الهالك',
            'TypeEn' =>'Depreciation',
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='الهالك';
        $Gen['TypeEn']='Depreciation';
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='الهالك';
        $Genn['TypeEn']='Depreciation';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M12'=>$x12]);
                     }
                     }
                }
                }
                }

                 }

         return view('admin.Accounts.Assets',['items'=>$items,'Code'=>$Code,'CostCenters'=>$CostCenters,'Coins'=>$Coins,'Branchs'=>$Branchs,'Safes'=>$Safes,'Ehlak'=>$Ehlak]);
    }

       public function AssetsSechduleFilter(){


           $From=request('From');
           $To=request('To');
           $Main_Account=request('Main_Account');
           $Asset_Type=request('Asset_Type');
           $Depreciation_Method=request('Depreciation_Method');

        $items=Assets::whereBetween('Operation_Date',[$From,$To])

                                    ->when(!empty($Main_Account), function ($query) use ($Main_Account) {
        return $query->where('Main_Account',$Main_Account);

                })

                           ->when(!empty($Asset_Type), function ($query) use ($Asset_Type) {
        return $query->where('Asset_Type',$Asset_Type);

                })


                                       ->when(!empty($Depreciation_Method), function ($query) use ($Depreciation_Method) {
        return $query->where('Depreciation_Method',$Depreciation_Method);

                })

           ->paginate(100);



            $res=Assets::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

                    $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();


        $Ehlak = AcccountingManual::
             where('Type',1)
              ->where('Parent',120)
              ->get();

            $CostCenters=CostCenter::all();
          $Coins=Coins::all();
               $Branchs=Branches::all();

             $itemsX=Assets::where('Asset_Net','>',1)->where('Asset_Type','مستهلك')->get();
                  $x1=date('Y')."-01";
                  $x2=date('Y')."-02";
                  $x3=date('Y')."-03";
                  $x4=date('Y')."-04";
                  $x5=date('Y')."-05";
                  $x6=date('Y')."-06";
                  $x7=date('Y')."-07";
                  $x8=date('Y')."-08";
                  $x9=date('Y')."-09";
                  $x10=date('Y')."-10";
                  $x11=date('Y')."-11";
                  $x12=date('Y')."-12";
                  foreach($itemsX as $item){

                if(!empty($item->Depreciation_Complex)){
                       if(!empty($item->Ehlak)){

  $date = new DateTime($item->Purchases_Date);
$date->modify('-1 month');
$CD=$date->format('Y-m');

                if($CD != date('Y-m')){

                     if($item->Purchases_Date == $x1){
                     if($item->M1 != $x1){

                    if($item->Asset_Type == 'مستهلك'){


                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }

                      Assets::where('id',$item->id)->update(['M1'=>$x1]);
                     }
                     }

                      if($item->Purchases_Date == $x2){
                           if($item->M2 != $x2){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                     Assets::where('id',$item->id)->update(['M2'=>$x2]);

                     }
                     }

                      if($item->Purchases_Date == $x3){
                           if($item->M3 != $x3){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M3'=>$x3]);
                     }
                     }

                      if($item->Purchases_Date == $x4){
                           if($item->M4 != $x4){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);


               if(is_numeric($item->Asset_Net)){
               $ff['Asset_Net']=$item->Asset_Net - $res ;
               }else{
                 $ff['Asset_Net']=0 - $res ;
               }
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M4'=>$x4]);
                     }
                     }

                      if($item->Purchases_Date == $x5){
                           if($item->M5 != $x5){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M5'=>$x5]);
                     }
                     }

                      if($item->Purchases_Date == $x6){
                           if($item->M6 != $x6){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M6'=>$x6]);
                     }
                     }

                      if($item->Purchases_Date == $x7){
                           if($item->M7 != $x7){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M7'=>$x7]);
                     }
                     }


                      if($item->Purchases_Date == $x8){
                           if($item->M8 != $x8){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M8'=>$x8]);
                     }
                     }


                      if($item->Purchases_Date == $x9){
                           if($item->M9 != $x9){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M9'=>$x9]);
                     }
                     }


                      if($item->Purchases_Date == $x10){
                           if($item->M10 != $x10){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M10'=>$x10]);
                     }
                     }


                      if($item->Purchases_Date == $x11){
                           if($item->M11 != $x11){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M11'=>$x11]);
                     }
                     }

                      if($item->Purchases_Date == $x12){
                           if($item->M12 != $x12){
                    if($item->Asset_Type == 'مستهلك'){

                            $res=$item->Annual_Depreciation / 12;
                                $rest=Journalizing::orderBy('id','desc')->first();

           if(!empty($rest->Code)){

              $CodeT=$rest->Code + 1 ;
           }else{
              $CodeT=1;

           }


     $IDT = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $CodeT,
            'Type' => trans('admin.Depreciation'),
            'Code_Type' => $item->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $item->Draw,
            'Coin' => $item->Coin,
            'Cost_Center' => $item->Cost_Center,
            'Total_Debaitor' => $res,
            'Total_Creditor' => $res,
            'Note' => $item->Note,

        )
    );


        $PRODUCTSS['Joun_ID']=$IDT;
        $PRODUCTSS['Debitor']=$res;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$item->Ehlak;
        $PRODUCTSS['Statement']=$item->Name;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$CodeT;
        $Gen['Code_Type']=$item->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.Depreciation');
        $Gen['Debitor']=$res;
        $Gen['Creditor']=0;
        $Gen['Statement']=$item->Name;
        $Gen['Draw']=$item->Draw;
        $Gen['Debitor_Coin']= $item->Draw * $res;
        $Gen['Creditor_Coin']=$item->Draw * 0;
        $Gen['Account']=$item->Ehlak;
        $Gen['Coin']= $item->Coin;
        $Gen['Cost_Center']= $item->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSSS['Joun_ID']=$IDT;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$res;
        $PRODUCTSSS['Account']=$item->Depreciation_Complex;
        $PRODUCTSSS['Statement']=$item->Name;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$CodeT;
        $Genn['Code_Type']=$item->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.Depreciation');
        $Genn['Debitor']=0;
        $Genn['Creditor']=$res;
        $Genn['Statement']=$item->Name;
        $Genn['Draw']=$item->Draw;
        $Genn['Debitor_Coin']= $item->Draw * 0;
        $Genn['Creditor_Coin']=$item->Draw * $res;
        $Genn['Account']=$item->Depreciation_Complex;
        $Genn['Coin']= $item->Coin;
        $Genn['Cost_Center']=$item->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

               $ff['Asset_Net']=$item->Asset_Net - $res ;
                    Assets::where('id',$item->id)->update($ff);





                    }
                           Assets::where('id',$item->id)->update(['M12'=>$x12]);
                     }
                     }
                }
                }
                }

                 }

         return view('admin.Accounts.Assets',['items'=>$items,'Code'=>$Code,'CostCenters'=>$CostCenters,'Coins'=>$Coins,'Branchs'=>$Branchs,'Safes'=>$Safes,'Ehlak'=>$Ehlak]);
    }

          public function AddAssets(){

              if(request('Asset_Type')  ==  trans('admin.consumer')){
                        $data= $this->validate(request(),[
             'Name'=>'required',
                     'Depreciation_Complex'=>'required',
             'Main_Account'=>'required',
             'Draw'=>'required',
             'Coin'=>'required',
             'Ehlak'=>'required',
               ],[
            'Name.required' => trans('admin.NameRequired'),
         ]);

              }else{

                   $data= $this->validate(request(),[
             'Name'=>'required',

               ],[
            'Name.required' => trans('admin.NameRequired'),
         ]);
              }



            $count=AcccountingManual::orderBy('id','desc')->where('Parent',request('Main_Account'))->count();
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',request('Main_Account'))->first();
            $codee=AcccountingManual::find(request('Main_Account'));

                if($count == 0){

                $x=$codee->Code.'01';
             $data['Code']=(int) $x ;

                }else{

                        $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;
            }
                $x= $codee->Code.$NewXY;
                  $data['Code']=(int) $x;

                }

         $data['Name']=request('Name');
            if(!empty(request('NameEn'))){
         $data['NameEn']=request('NameEn');
          }else{
        $data['NameEn']=request('Name');
          }
         $data['Type']=1;
         $data['Parent']=request('Main_Account');
         $data['Note']=null;
         $data['User']=auth()->guard('admin')->user()->id;
         AcccountingManual::create($data);


         $Acc=AcccountingManual::orderBy('id','desc')->first();


                     $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AssetsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Image']=$image_url;

             }else{
                 $data['Image']=null;
             }


              if(request('Asset_Type') == 'consumer'){
                 $Asset_TypeAr='مستهلك';
                    $Asset_TypeEn='consumer';
              }else{
                       $Asset_TypeAr='غير مستهلك';
                    $Asset_TypeEn='unconsumed';
              }


                  if(request('Asset_Type') == 'Fixed'){
                 $DeprAr='ثابت';
                    $DeprEn='Fixed';
              }else{
                       $DeprAr='متناقص';
                    $DeprEn='decreasing';
              }



         $dataa['Name']=request('Name');
             if(!empty(request('NameEn'))){
         $dataa['NameEn']=request('NameEn');
          }else{
        $dataa['NameEn']=request('Name');
          }
         $dataa['Asset_Type']=$Asset_TypeAr;
         $dataa['Asset_Type_En']=$Asset_TypeEn;
         $dataa['Depreciation_Method']=$DeprAr;
         $dataa['Depreciation_Method_En']=$DeprEn;
         $dataa['Purchases_Date']=date('Y-m', strtotime('+1 month'));
         $dataa['Operation_Date']=request('Operation_Date');
         $dataa['Cost']=request('Cost');
         $dataa['Previous_Depreciation']=request('Previous_Depreciation');
         $dataa['Asset_Net']=request('Asset_Net');
         $dataa['Annual_Depreciation_Ratio']=request('Annual_Depreciation_Ratio');
         $dataa['Annual_Depreciation']=request('Annual_Depreciation');
         $dataa['Life_Span']=request('Life_Span');
         $dataa['Note']=request('Note');
         $dataa['Depreciation_Expenses']=request('Depreciation_Expenses');
         $dataa['Depreciation_Complex']=request('Depreciation_Complex');
         $dataa['Main_Account']=request('Main_Account');
         $dataa['Draw']=request('Draw');
         $dataa['Coin']=request('Coin');
         $dataa['Branch']=request('Branch');
         $dataa['Cost_Center']=request('Cost_Center');
         $dataa['Sort_Asset']=request('Sort_Asset');
         $dataa['Vendor']=request('Vendor');
         $dataa['Safe']=request('Safe');
         $dataa['Ehlak']=request('Ehlak');
         $dataa['Payment_Method']=request('Payment_Method');
         $dataa['Account']=$Acc->id;
         $dataa['User']=auth()->guard('admin')->user()->id;

         Assets::create($dataa);


            $last=Assets::orderBy('id','desc')->first();

                 $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }


        $c= DB::select("SELECT last_value FROM assets_arr_seq");
      $f=array_shift($c);
      $z=end($f);
  $CodeT=$z;



            if(request('Asset_Type')  ==  'consumer'){

                $money=request('Asset_Net');

            }else{

            $money= request('Cost');
            }



          if(request('Sort_Asset') == 1){
        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'شراء أصل',
            'TypeEn' =>'Purchases Asset',
            'Code_Type' => $CodeT,
            'Date' => date('Y-m-d'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => $money,
            'Total_Creditor' => $money,
            'Note' => request('Note'),

        )
    );


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$money;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$last->Account;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='شراء أصل';
        $Gen['TypeEn']='Purchases Asset';
        $Gen['Debitor']=$money;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $money;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$last->Account;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);



        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$money;
        $PRODUCTSSS['Account']=45;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='شراء أصل';
        $Genn['TypeEn']='Purchases Asset';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$money;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * $money;
        $Genn['Account']=45;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

          }elseif(request('Sort_Asset') == 2){

                 $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' =>'شراء أصل',
            'TypeEn' =>'Purchases Asset',
            'Code_Type' => $CodeT,
            'Date' => date('Y-m-d'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => $money,
            'Total_Creditor' => $money,
            'Note' => request('Note'),

        )
    );


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=$money;
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;



         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='شراء أصل';
        $Gen['TypeEn']='Purchases Asset';
        $Gen['Debitor']=$money;
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * $money;
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);



        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=$money;
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='شراء أصل';
        $Genn['TypeEn']='Purchases Asset';
        $Genn['Debitor']=0;
        $Genn['Creditor']=$money;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * $money;
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);




        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=$money;
        $PRODUCTSS['Account']=request('Vendor');
        $PRODUCTSS['Statement']=null;

         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='شراء أصل';
        $Gen['TypeEn']='Purchases Asset';
        $Gen['Debitor']=0;
        $Gen['Creditor']=$money;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * 0;
        $Gen['Creditor_Coin']=request('Draw') * $money;
        $Gen['Account']=request('Vendor');
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);



        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=$money;
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=$last->Account;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$CodeT;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='شراء أصل';
        $Genn['TypeEn']='Purchases Asset';
        $Genn['Debitor']=$money;
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * $money;
        $Genn['Creditor_Coin']=request('Draw') * 0;
        $Genn['Account']=$last->Account;
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);


          }


           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الاصول';
           $dataUser['ScreenEn']='Assets';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='Add New';
           $dataUser['Explain']=request('Name');

              if(!empty(request('NameEn'))){
          $dataUser['ExplainEn']=request('NameEn');
          }else{
        $dataUser['ExplainEn']=request('Name');

          }
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function DeleteAssets($id){

        $del=Assets::find($id);
         AcccountingManual::where('id',$del->Account)->delete();
        GeneralDaily::where('Code_Type',$del->Code)->where('Type','شراء أصل')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','شراء أصل')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','الهالك')->delete();
         GeneralDaily::where('Code_Type',$del->Code)->where('Type','الهالك')->delete();

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());

            $dataUser['Screen']='الاصول';
           $dataUser['ScreenEn']='Assets';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$del->Name;
           $dataUser['ExplainEn']=$del->NameEn;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

       public function AssetSale(){


           $ass=Assets::find(request('ID'));
       AcccountingManual::where('id',$ass->Account)->delete();


                 $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }


                 $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'بيع أصل',
            'TypeEn' => 'Asset Sale',
            'Code_Type' => $ass->Code,
            'Date' => date('Y-m-d'),
            'Draw' => $ass->Draw,
            'Coin' => $ass->Coin,
            'Cost_Center' => $ass->Cost_Center,
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => $ass->Note,

        )
    );




        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=request('Safe');
        $PRODUCTSS['Statement']=null;



         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$ass->Code;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='بيع أصل';
        $Gen['TypeEn']='Asset Sale';
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$ass->Draw;
        $Gen['Debitor_Coin']= $ass->Draw * request('Amount');
        $Gen['Creditor_Coin']=$ass->Draw * 0;
        $Gen['Account']=request('Safe');
        $Gen['Coin']= $ass->Coin;
        $Gen['Cost_Center']= $ass->Cost_Center;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);



        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=request('Amount');
        $PRODUCTSSS['Account']=request('Client');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$ass->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='بيع أصل';
        $Genn['TypeEn']='Asset Sale';
        $Genn['Debitor']=0;
        $Genn['Creditor']=request('Amount');
        $Genn['Statement']=null;
        $Genn['Draw']=$ass->Draw;
        $Genn['Debitor_Coin']= $ass->Draw * 0;
        $Genn['Creditor_Coin']=$ass->Draw * request('Amount');
        $Genn['Account']=request('Client');
        $Genn['Coin']= $ass->Coin;
        $Genn['Cost_Center']=$ass->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);




            $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=request('Amount');
        $PRODUCTSSS['Creditor']=0;
        $PRODUCTSSS['Account']=request('Client');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$ass->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='بيع أصل';
        $Genn['TypeEn']='Asset Sale';
        $Genn['Debitor']=request('Amount');
        $Genn['Creditor']=0;
        $Genn['Statement']=null;
        $Genn['Draw']=$ass->Draw;
        $Genn['Debitor_Coin']= $ass->Draw * request('Amount');
        $Genn['Creditor_Coin']=$ass->Draw * 0;
        $Genn['Account']=request('Client');
        $Genn['Coin']= $ass->Coin;
        $Genn['Cost_Center']=$ass->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);



            $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=request('Amount');
        $PRODUCTSSS['Account']=48;
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=$ass->Code;
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']='بيع أصل';
        $Genn['TypeEn']='Asset Sale';
        $Genn['Debitor']=0;
        $Genn['Creditor']=request('Amount');
        $Genn['Statement']=null;
        $Genn['Draw']=$ass->Draw;
        $Genn['Debitor_Coin']= $ass->Draw * 0;
        $Genn['Creditor_Coin']=$ass->Draw * request('Amount');
        $Genn['Account']=48;
        $Genn['Coin']= $ass->Coin;
        $Genn['Cost_Center']=$ass->Cost_Center;
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الاصول';
           $dataUser['ScreenEn']='Assets';
           $dataUser['Type']='بيع أصل';
           $dataUser['TypeEn']='Asset Sale';
           $dataUser['Explain']=$ass->Name;
           $dataUser['ExplainEn']=$ass->NameEn;
           UsersMoves::create($dataUser);

            Assets::where('id',$ass->id)->delete();

             session()->flash('success',trans('admin.Asset_Sale'));
             return back();

    }


   //Assets Expenses
     public function AssetExpensesPage(){
        $items=AssetsExpenses::paginate(100);
           $Assets=Assets::all();
            $res=AssetsExpenses::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }

            $CostCenters=CostCenter::all();
          $Coins=Coins::all();

         $Safes=AcccountingManual::
          where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();

         return view('admin.Accounts.AssetsExpenses',['items'=>$items,'Code'=>$Code,'CostCenters'=>$CostCenters,'Coins'=>$Coins,'Safes'=>$Safes,'AssetsList'=>$Assets]);
    }

     public function AddAssetsExpenses(){

        $data= $this->validate(request(),[
             'Name'=>'required',
               ],[
            'Name.required' => trans('admin.NameRequired'),
         ]);



         $dataa['Code']=request('Code');
         $dataa['Date']=request('Date');
         $dataa['Name']=request('Name');
         $dataa['Amount']=request('Amount');
         $dataa['Asset']=request('Asset');
         $dataa['Safe']=request('Safe');
         $dataa['Draw']=request('Draw');
         $dataa['Coin']=request('Coin');
         $dataa['Cost_Center']=request('Cost_Center');
         $dataa['User']=auth()->guard('admin')->user()->id;

         AssetsExpenses::create($dataa);


         $last=Assets::find(request('Asset'));
         $new=$last->Asset_Net + request('Amount') ;
         $zz=($last->Annual_Depreciation_Ratio / 100 )  * $new;

         $ass['Asset_Net']=$new;
         $ass['Annual_Depreciation']=$zz;
         Assets::where('id',$last->id)->update($ass);


                 $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }

        $ID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => trans('admin.AssetExpenses'),
            'Code_Type' => request('Code'),
            'Date' => date('Y-m-d'),
            'Draw' => request('Draw'),
            'Coin' => request('Coin'),
            'Cost_Center' => request('Cost_Center'),
            'Total_Debaitor' => request('Amount'),
            'Total_Creditor' => request('Amount'),
            'Note' => request('Note'),

        )
    );


        $PRODUCTSS['Joun_ID']=$ID;
        $PRODUCTSS['Debitor']=request('Amount');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$last->Depreciation_Expenses;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=request('Code');
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']=trans('admin.AssetExpenses');
        $Gen['Debitor']=request('Amount');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=request('Draw');
        $Gen['Debitor_Coin']= request('Draw') * request('Amount');
        $Gen['Creditor_Coin']=request('Draw') * 0;
        $Gen['Account']=$last->Depreciation_Expenses;
        $Gen['Coin']= request('Coin');
        $Gen['Cost_Center']= request('Cost_Center');
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);



        $PRODUCTSSS['Joun_ID']=$ID;
        $PRODUCTSSS['Debitor']=0;
        $PRODUCTSSS['Creditor']=request('Amount');
        $PRODUCTSSS['Account']=request('Safe');
        $PRODUCTSSS['Statement']=null;
         JournalizingDetails::create($PRODUCTSSS);

        $Genn['Code']=$Code;
        $Genn['Code_Type']=request('Code');
        $Genn['Date']=date('Y-m-d');
        $Genn['Type']=trans('admin.AssetExpenses');
        $Genn['Debitor']=0;
        $Genn['Creditor']=request('Amount');
        $Genn['Statement']=null;
        $Genn['Draw']=request('Draw');
        $Genn['Debitor_Coin']= request('Draw') * 0;
        $Genn['Creditor_Coin']=request('Draw') * request('Amount');
        $Genn['Account']=request('Safe');
        $Genn['Coin']= request('Coin');
        $Genn['Cost_Center']= request('Cost_Center');
        $Genn['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Genn);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']=trans('admin.AssetExpenses');
           $dataUser['Type']=trans('admin.AddNew');
           $dataUser['Explain']=request('Name');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }


    // ===  Filters ====
      public function AllCoins() {


        if(app()->getLocale() == 'ar' ){
       $states = Coins::orderBy("id",'asc')->pluck("Arabic_Name","id");
        }else{

    $states = Coins::orderBy("id",'asc')->pluck("English_Name","id");

        }

       return response()->json($states);

    }

      public function AllCostss() {


        if(app()->getLocale() == 'ar' ){
       $states = CostCenter::orderBy("id",'asc')->pluck("Arabic_Name","id");
        }else{

    $states = CostCenter::orderBy("id",'asc')->pluck("English_Name","id");

        }

       return response()->json($states);

    }

      public function AllSubAccounts() {


        if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',1)->pluck("Name","id");
        }else{

          $states = AcccountingManual::where('Type',1)->pluck("NameEn","id");
        }
       return response()->json($states);

    }

        public function AllSubAccountsMsrofat() {


                if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',1)->where('Parent',55)->pluck("Name","id");
                }else{
          $states = AcccountingManual::where('Type',1)->where('Parent',55)->pluck("NameEn","id");
                }


       return response()->json($states);

    }

       public function AllMainAccounts() {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',0)->pluck("Name","id");
   }else{
         $states = AcccountingManual::where('Type',0)->pluck("NameEn","id");

   }
       return response()->json($states);

    }

       public function AllUsers() {

   if(app()->getLocale() == 'ar' ){
       $states = Admin::where('hidden',0)->pluck("name","id");
   }else{
        $states = Admin::where('hidden',0)->pluck("nameEn","id");
   }
       return response()->json($states);

    }

      public function AllSafes() {


         if(app()->getLocale() == 'ar' ){
           if(auth()->guard('admin')->user()->emp == 0){

       $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->pluck("Name","id");
     }else{

            if(!empty(auth()->guard('admin')->user()->safe)){

              $states = AcccountingManual::
          where('Type',1)
              ->where('id',auth()->guard('admin')->user()->safe)
              ->pluck("Name","id");

            }else{

           $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->pluck("Name","id");
            }

     }

         }else{

                 if(auth()->guard('admin')->user()->emp == 0){

       $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->pluck("NameEn","id");
     }else{

            if(!empty(auth()->guard('admin')->user()->safe)){

              $states = AcccountingManual::
          where('Type',1)
              ->where('id',auth()->guard('admin')->user()->safe)
              ->pluck("NameEn","id");

            }else{

           $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->pluck("NameEn","id");
            }

     }


         }


       return response()->json($states);

    }

         public function SafeBalanceFilter($id) {

           $debt=GeneralDaily::where('Account',$id)->get()->sum('Debitor');
           $crdt=GeneralDaily::where('Account',$id)->get()->sum('Creditor');
            $dif=$debt - $crdt ;


       $states = [$dif,$dif];

       return response()->json($states);

    }

  public function AccountNameFilter($id) {

           $x = AcccountingManual::where("id",$id)->first();
     $states=[];

         if(app()->getLocale() == 'ar' ){

  $states += ["name" => $x->Name ,'code' => $x->Code ,'ID' => $x->id];
         }else{

       $states += ["name" => $x->NameEn ,'code' => $x->Code ,'ID' => $x->id];
         }


       return response()->json($states);

    }

     public function AllBanksAccounts() {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',29)
              ->pluck("Name","id");
   }else{
          $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',29)
              ->pluck("NameEn","id");

   }

       return response()->json($states);

    }
      public function AllAccounts() {

           if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::orderBy('Code','asc')->pluck("Name","id");
           }else{
      $states = AcccountingManual::orderBy('Code','asc')->pluck("NameEn","id");
           }
       return response()->json($states);

    }

     public function AllCustomers() {

     if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',24)
              ->pluck("Name","id");
     }else{
             $states = AcccountingManual::
          where('Type',1)
              ->where('Parent',24)
              ->pluck("NameEn","id");

     }

       return response()->json($states);

    }

        public function AllEmps() {

  if(app()->getLocale() == 'ar' ){
       $states = Employess::orderBy('id','asc')
              ->where('EmpSort',1)->where('Active',1)
              ->pluck("Name","id");
  }else{
           $states = Employess::orderBy('id','asc')
              ->where('EmpSort',1)->where('Active',1)
              ->pluck("NameEn","id");

  }
       return response()->json($states);

    }

        public function AllVendors() {

                 $Def=PurchasesDefaultData::orderBy('id','desc')->first();


              if(app()->getLocale() == 'ar' ){
                     if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Vendors::where('Responsible',auth()->guard('admin')->user()->emp)->get();

                   foreach($Custs as $Cu){

                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->Name  ];
                   }



               }else{

               if($Def->V_and_C == 1){

                          $states = AcccountingManual::
                where('Type',1)

             ->whereIn('Parent',[37,24])

             ->pluck("Name","id");

               }elseif($Def->V_and_C == 0){

            $states = AcccountingManual::
                where('Type',1)
                ->whereIn('Parent',[37])
             ->pluck("Name","id");

               }
                     }
              }else{
       if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Vendors::where('Responsible',auth()->guard('admin')->user()->emp)->get();

                   foreach($Custs as $Cu){

                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->NameEn  ];
                   }



               }else{

               if($Def->V_and_C == 1){

                          $states = AcccountingManual::
                where('Type',1)

             ->whereIn('Parent',[37,24])

             ->pluck("NameEn","id");

               }elseif($Def->V_and_C == 0){

            $states = AcccountingManual::
                where('Type',1)
                ->whereIn('Parent',[37])
             ->pluck("NameEn","id");

               }
                     }

              }

       return response()->json($states);

    }


       public function AllClientsFilter() {


                 $DefCr=CrmDefaultData::orderBy('id','desc')->first();

             if(app()->getLocale() == 'ar' ){

           if($DefCr->Client_Delegate == 1){

               if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Customers::where('Responsible',auth()->guard('admin')->user()->emp)->get();

                   foreach($Custs as $Cu){

                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->Name  ];

                   }



               }else{

                    $Def=SalesDefaultData::orderBy('id','desc')->first();
      if($Def->V_and_C == 1 and $Def->Empp == 1){

                          $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37,53,55])
             ->pluck("Name","id");

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37])
             ->pluck("Name","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){

                $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,53,55])
             ->pluck("Name","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->where('Parent',24)
             ->pluck("Name","id");

               }

               }



           }else{

                 $Def=SalesDefaultData::orderBy('id','desc')->first();
      if($Def->V_and_C == 1 and $Def->Empp == 1){

                          $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37,53,55])
             ->pluck("Name","id");

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37])
             ->pluck("Name","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){

                $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,53,55])
             ->pluck("Name","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->where('Parent',24)
             ->pluck("Name","id");

               }

           }

             }else{

          if($DefCr->Client_Delegate == 1){

               if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Customers::where('Responsible',auth()->guard('admin')->user()->emp)->get();

                   foreach($Custs as $Cu){


                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->NameEn  ];
                   }



               }else{

                    $Def=SalesDefaultData::orderBy('id','desc')->first();
      if($Def->V_and_C == 1 and $Def->Empp == 1){

                          $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37,53,55])
             ->pluck("NameEn","id");

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37])
             ->pluck("NameEn","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){

                $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,53,55])
             ->pluck("NameEn","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->where('Parent',24)
             ->pluck("NameEn","id");

               }

               }



           }else{

                 $Def=SalesDefaultData::orderBy('id','desc')->first();
      if($Def->V_and_C == 1 and $Def->Empp == 1){

                          $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37,53,55])
             ->pluck("NameEn","id");

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,37])
             ->pluck("NameEn","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){

                $states = AcccountingManual::
                where('Type',1)
              ->whereIn('Parent',[24,53,55])
             ->pluck("NameEn","id");

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $states = AcccountingManual::
                where('Type',1)
              ->where('Parent',24)
             ->pluck("NameEn","id");

               }

           }

             }



       return response()->json($states);

    }

 public function AllShips() {

       if(app()->getLocale() == 'ar' ){
   $states = ShippingCompany::orderBy('id','asc')
              ->pluck("Name","id");
       }else{
     $states = ShippingCompany::orderBy('id','asc')
              ->pluck("NameEn","id");

       }

       return response()->json($states);

    }

    public function AllVend() {


           if(app()->getLocale() == 'ar' ){
   $states = Vendors::orderBy('id','asc')
              ->pluck("Name","id");
           }else{
           $states = Vendors::orderBy('id','asc')
              ->pluck("NameEn","id");
           }

       return response()->json($states);

    }

          public function AllCli() {

          if(app()->getLocale() == 'ar' ){
   $states = Customers::orderBy('id','asc')
              ->pluck("Name","id");
          }else{

            $states = Customers::orderBy('id','asc')
              ->pluck("NameEn","id");
          }
       return response()->json($states);

    }

     public function AllMainAssetsAccounts() {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',0)->where('Parent',21)->pluck("Name","id");
   }else{

       $states = AcccountingManual::where('Type',0)->where('Parent',21)->pluck("NameEn","id");
   }


       return response()->json($states);

    }

         public function AllAccountsExpenses() {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',1)->where('Parent',122)->pluck("Name","id");
   }else{
       $states = AcccountingManual::where('Type',1)->where('Parent',122)->pluck("NameEn","id");
   }
       return response()->json($states);

    }

         public function AllAccountsComplex() {

if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',1)->where('Parent',119)->pluck("Name","id");
}else{
    $states = AcccountingManual::where('Type',1)->where('Parent',119)->pluck("NameEn","id");
}
       return response()->json($states);

    }


    //Ajax Search Filters
     public function AllMainAssetsAccountsJ($id) {

if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
          where('Name','ILIKE', "%{$id}%")
          ->where('Type',0)
              ->where('Parent',21)
              ->pluck("Name","id");
}else{

     $states = AcccountingManual::
          where('NameEn','ILIKE', "%{$id}%")
          ->where('Type',0)
              ->where('Parent',21)
              ->pluck("NameEn","id");

}

       return response()->json($states);

    }

     public function AllAccountsExpensesJ($id) {

if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)->where('Parent',122)->pluck("Name","id");
}else{
         $states = AcccountingManual::where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)->where('Parent',122)->pluck("NameEn","id");

}
       return response()->json($states);

    }

      public function AllAccountsComplexJ($id) {

if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Name','ILIKE', "%{$id}%")->where('Type',1)->where('Parent',119)->pluck("Name","id");
}else{

     $states = AcccountingManual::where('NameEn','ILIKE', "%{$id}%")->where('Type',1)->where('Parent',119)->pluck("NameEn","id");
}
       return response()->json($states);

    }

      public function AllVendorsJ($id) {

                 $Def=PurchasesDefaultData::orderBy('id','desc')->first();


        if(app()->getLocale() == 'ar' ){

           if($Def->V_and_C == 0){
               $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
              ->where('Type',1)
              ->where('Parent',37)
             ->pluck("Name","id");

     }elseif($Def->V_and_C == 1){

                   $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
                   ->where('Parent',37)
              ->orWhere('Parent',24)
               ->pluck("Name","id");



     }
        }else{
      if($Def->V_and_C == 0){
               $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
              ->where('Type',1)
              ->where('Parent',37)
             ->pluck("NameEn","id");

     }elseif($Def->V_and_C == 1){

                   $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
                   ->where('Parent',37)
              ->orWhere('Parent',24)
               ->pluck("NameEn","id");



     }

        }



       return response()->json($states);

    }

      public function AllClientsFilterJ($id) {


                 $Def=SalesDefaultData::orderBy('id','desc')->first();

      if(app()->getLocale() == 'ar' ){
           if($Def->V_and_C == 0){
               $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
              ->where('Parent',24)
             ->pluck("Name","id");
     }elseif($Def->V_and_C == 1){

                   $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
                ->whereIn('Parent', [24, 37])
             ->pluck("Name","id");



     }
      }else{

         if($Def->V_and_C == 0){
               $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
              ->where('Parent',24)
             ->pluck("NameEn","id");
     }elseif($Def->V_and_C == 1){

                   $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
                ->whereIn('Parent', [24, 37])
             ->pluck("NameEn","id");



     }
      }



       return response()->json($states);

    }

      public function MainAccountss($id) {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                    ->pluck("Name","id");
   }else{
         $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                    ->pluck("NameEn","id");

   }

       return response()->json($states);

    }

      public function AllSubAccountsJ($id) {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
           where('Name','ILIKE', "%{$id}%")->where('Type',1)->pluck("Name","id");
   }else{
         $states = AcccountingManual::
           where('NameEn','ILIKE', "%{$id}%")->where('Type',1)->pluck("NameEn","id");

   }

       return response()->json($states);

    }

      public function AllSafesJ($id) {


         if(app()->getLocale() == 'ar' ){
                 if(auth()->guard('admin')->user()->emp == 0){

     $states = AcccountingManual::
          where('Name','ILIKE', "%{$id}%")->where('Type',1)
                 ->whereIn('Parent', [28, 29])
              ->pluck("Name","id");
     }else{

            if(!empty(auth()->guard('admin')->user()->safe)){


                 $states = AcccountingManual::
          where('Name','ILIKE', "%{$id}%")->where('Type',1)
            ->where('id',auth()->guard('admin')->user()->safe)
              ->pluck("Name","id");



            }else{

          $states = AcccountingManual::
          where('Name','ILIKE', "%{$id}%")->where('Type',1)
                 ->whereIn('Parent', [28, 29])
              ->pluck("Name","id");
            }

     }
         }else{

             if(auth()->guard('admin')->user()->emp == 0){

     $states = AcccountingManual::
          where('NameEn','ILIKE', "%{$id}%")->where('Type',1)
                 ->whereIn('Parent', [28, 29])
              ->pluck("NameEn","id");
     }else{

            if(!empty(auth()->guard('admin')->user()->safe)){


                 $states = AcccountingManual::
          where('NameEn','ILIKE', "%{$id}%")->where('Type',1)
            ->where('id',auth()->guard('admin')->user()->safe)
              ->pluck("NameEn","id");



            }else{

          $states = AcccountingManual::
          where('NameEn','ILIKE', "%{$id}%")->where('Type',1)
                 ->whereIn('Parent', [28, 29])
              ->pluck("NameEn","id");
            }

     }
         }

       return response()->json($states);

    }

      public function AllBanksAccountsJ($id) {

       if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
          where('Name','ILIKE', "%{$id}%")->where('Type',1)
              ->where('Parent',29)
              ->pluck("Name","id");
       }else{

          $states = AcccountingManual::
          where('NameEn','ILIKE', "%{$id}%")->where('Type',1)
              ->where('Parent',29)
              ->pluck("NameEn","id");
       }

       return response()->json($states);

    }

        public function AllCoinsJ($id) {


        if(app()->getLocale() == 'ar' ){
       $states = Coins::orderBy("id",'asc')->
           where('Arabic_Name','ILIKE', "%{$id}%")->
           pluck("Arabic_Name","id");
        }else{

    $states = Coins::orderBy("id",'asc')->
        where('English_Name','ILIKE', "%{$id}%")
        ->pluck("English_Name","id");

        }

       return response()->json($states);

    }

      public function AllCostssJ($id) {


        if(app()->getLocale() == 'ar' ){
       $states = CostCenter::orderBy("id",'asc')->
               where('Arabic_Name','ILIKE', "%{$id}%")
           ->pluck("Arabic_Name","id");
        }else{

    $states = CostCenter::orderBy("id",'asc')->
            where('English_Name','ILIKE', "%{$id}%")
        ->pluck("English_Name","id");

        }

       return response()->json($states);

    }

       public function AllUsersJ($id) {

               if(app()->getLocale() == 'ar' ){
       $states = Admin::
           where('name','ILIKE', "%{$id}%")->where('hidden',0)->pluck("name","id");
               }else{
               $states = Admin::
           where('nameEn','ILIKE', "%{$id}%")->where('hidden',0)->pluck("nameEn","id");

               }
       return response()->json($states);

    }

      public function AllCustomersJ($id) {

    if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
              where('Name','ILIKE', "%{$id}%")
              ->where('Type',1)
              ->where('Parent',24)
              ->pluck("Name","id");
    }else{

             $states = AcccountingManual::
              where('NameEn','ILIKE', "%{$id}%")
              ->where('Type',1)
              ->where('Parent',24)
              ->pluck("NameEn","id");
    }

       return response()->json($states);

    }

     public function AllClientsJ($id) {


                    if(app()->getLocale() == 'ar' ){
               $states = Customers::
                where('Name','ILIKE', "%{$id}%")
                ->orWhere('Company_Name','ILIKE', "%{$id}%")
             ->pluck("Name","id");
                    }else{

                         $states = Customers::
                where('NameEn','ILIKE', "%{$id}%")
                ->orWhere('Company_Name','ILIKE', "%{$id}%")
             ->pluck("NameEn","id");
                    }


       return response()->json($states);

    }

     public function AllEmpsJ($id) {

       if(app()->getLocale() == 'ar' ){
       $states = Employess::orderBy('id','asc')

           ->where('Name','ILIKE', "%{$id}%")
           ->orWhere('Code','ILIKE', "%{$id}%")
           ->where('EmpSort',1)->where('Active',1)
              ->pluck("Name","id");
       }else{

               $states = Employess::orderBy('id','asc')

           ->where('NameEn','ILIKE', "%{$id}%")
           ->orWhere('Code','ILIKE', "%{$id}%")
           ->where('EmpSort',1)->where('Active',1)
              ->pluck("NameEn","id");

       }

       return response()->json($states);

    }

     public function AllShipsJ($id) {


             if(app()->getLocale() == 'ar' ){
   $states = ShippingCompany::orderBy('id','asc')
        ->where('Name','ILIKE', "%{$id}%")
              ->pluck("Name","id");
             }else{
           $states = ShippingCompany::orderBy('id','asc')
        ->where('NameEn','ILIKE', "%{$id}%")
              ->pluck("NameEn","id");

             }

       return response()->json($states);

    }

    public function AllVendJ($id) {

         if(app()->getLocale() == 'ar' ){
   $states = Vendors::orderBy('id','asc')
        ->where('Name','ILIKE', "%{$id}%")
              ->pluck("Name","id");
         }else{
        $states = Vendors::orderBy('id','asc')
        ->where('NameEn','ILIKE', "%{$id}%")
              ->pluck("NameEn","id");

         }
       return response()->json($states);

    }

          public function AllCliJ($id) {

            if(app()->getLocale() == 'ar' ){
   $states = Customers::orderBy('id','asc')
        ->where('Name','ILIKE', "%{$id}%")
              ->pluck("Name","id");
            }else{

 $states = Customers::orderBy('id','asc')
        ->where('NameEn','ILIKE', "%{$id}%")
              ->pluck("NameEn","id");

            }
       return response()->json($states);

    }

      public function AllVendorsJS($id) {

                 $Def=PurchasesDefaultData::orderBy('id','desc')->first();

             if(app()->getLocale() == 'ar' ){
                     if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Vendors::where('Responsible',auth()->guard('admin')->user()->emp)
                       ->where('Name','ILIKE', "%{$id}%")
                        ->orWhere('Phone','ILIKE', "%{$id}%")
                        ->orWhere('Code',$id)
                       ->get();

                   foreach($Custs as $Cu){

                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->Name  ];
                   }



               }else{
    if($Def->V_and_C == 1){


            $Emps=Employess::where('Code',$id)
              ->first();

                $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone',$id)
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone',$id)
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37])
               ->pluck("Name","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37])
               ->pluck("Name","id");

               }

         if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37])
               ->pluck("Name","id");

               }



               }elseif($Def->V_and_C == 0){

                 $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone',$id)
              ->first();

         if(!empty($Vendo)){


                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("Name","id");


               }else{


                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->where('Parent', 37)
               ->pluck("Name","id");

               }


               }

                     }
             }else{

                 if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Vendors::where('Responsible',auth()->guard('admin')->user()->emp)
                       ->where('NameEn','ILIKE', "%{$id}%")
                        ->orWhere('Phone','ILIKE', "%{$id}%")
                        ->orWhere('Code',$id)
                       ->get();

                   foreach($Custs as $Cu){

                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->NameEn  ];
                   }



               }else{
    if($Def->V_and_C == 1){


            $Emps=Employess::where('Code',$id)
              ->first();

                $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone',$id)
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone',$id)
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37])
               ->pluck("NameEn","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37])
               ->pluck("NameEn","id");

               }

         if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37])
               ->pluck("NameEn","id");

               }



               }elseif($Def->V_and_C == 0){

                 $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone',$id)
              ->first();

         if(!empty($Vendo)){


                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("NameEn","id");


               }else{


                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->where('Parent', 37)
               ->pluck("NameEn","id");

               }


               }

                     }

             }

       return response()->json($states);

    }

       public function AllClientsFilterJS($id) {



                    $DefCr=CrmDefaultData::orderBy('id','desc')->first();

                  if(app()->getLocale() == 'ar' ){
           if($DefCr->Client_Delegate == 1){

               if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Customers::where('Responsible',auth()->guard('admin')->user()->emp)
                       ->where('Name','ILIKE', "%{$id}%")
                        ->orWhere('Phone','ILIKE', "%{$id}%")
                        ->orWhere('Code',$id)
                       ->get();

                   foreach($Custs as $Cu){

                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->Name  ];
                   }



               }else{

                         $Def=SalesDefaultData::orderBy('id','desc')->first();

               if($Def->V_and_C == 1 and $Def->Empp == 1){


            $Emps=Employess::where('Code',$id)
              ->first();

                $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("Name","id");

               }

     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("Name","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("Name","id");

               }

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

           $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("Name","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("Name","id");

               }


               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){


     $Emps=Employess::where('Code',$id)
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("Name","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("Name","id");

               }

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->where('Parent',24)
               ->pluck("Name","id");

               }


               }

               }

           }else{

        $Def=SalesDefaultData::orderBy('id','desc')->first();

               if($Def->V_and_C == 1 and $Def->Empp == 1){


            $Emps=Employess::where('Code',$id)
              ->first();

                $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("Name","id");

               }

     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("Name","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("Name","id");

               }

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

           $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("Name","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("Name","id");

               }


               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){


     $Emps=Employess::where('Code',$id)
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("Name","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("Name","id");

               }

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("Name","id");


               }else{

                  $states = AcccountingManual::
                where('Name','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->where('Parent',24)
               ->pluck("Name","id");

               }


               }

           }

                  }else{

           if($DefCr->Client_Delegate == 1){

               if(auth()->guard('admin')->user()->emp != 0){


                   $states=[];

                   $Custs=Customers::where('Responsible',auth()->guard('admin')->user()->emp)
                       ->where('NameEn','ILIKE', "%{$id}%")
                        ->orWhere('Phone','ILIKE', "%{$id}%")
                        ->orWhere('Code',$id)
                       ->get();

                   foreach($Custs as $Cu){

                       $states +=[ $Cu->Account =>  $Cu->Account()->first()->NameEn  ];
                   }



               }else{

                         $Def=SalesDefaultData::orderBy('id','desc')->first();

               if($Def->V_and_C == 1 and $Def->Empp == 1){


            $Emps=Employess::where('Code',$id)
              ->first();

                $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("NameEn","id");

               }

     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("NameEn","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("NameEn","id");

               }

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

           $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("NameEn","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("NameEn","id");

               }


               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){


     $Emps=Employess::where('Code',$id)
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("NameEn","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("NameEn","id");

               }

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->where('Parent',24)
               ->pluck("NameEn","id");

               }


               }

               }

           }else{

        $Def=SalesDefaultData::orderBy('id','desc')->first();

               if($Def->V_and_C == 1 and $Def->Empp == 1){


            $Emps=Employess::where('Code',$id)
              ->first();

                $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("NameEn","id");

               }

     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("NameEn","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,37,53,55])
               ->pluck("NameEn","id");

               }

               }elseif($Def->V_and_C == 1 and $Def->Empp == 0){

           $Vendo=Vendors::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


     if(!empty($Vendo)){

                 $states = AcccountingManual::
              where('id',$Vendo->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("NameEn","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24, 37])
               ->pluck("NameEn","id");

               }


               }elseif($Def->V_and_C == 0 and $Def->Empp == 1){


     $Emps=Employess::where('Code',$id)
              ->first();

        $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();


      if(!empty($Emps)){

                 $states = AcccountingManual::
              where('id',$Emps->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("NameEn","id");

               }

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->whereIn('Parent', [24,53,55])
               ->pluck("NameEn","id");

               }

               }elseif($Def->V_and_C == 0 and $Def->Empp == 0){

            $Cust=Customers::where('Code',$id)
              ->orWhere('Phone','ILIKE', "%{$id}%")
              ->first();

       if(!empty($Cust)){

                 $states = AcccountingManual::
              where('id',$Cust->Account)
             ->pluck("NameEn","id");


               }else{

                  $states = AcccountingManual::
                where('NameEn','ILIKE', "%{$id}%")
                ->where('Type',1)
               ->where('Parent',24)
               ->pluck("NameEn","id");

               }


               }

           }
                  }

       return response()->json($states);

    }

           public function AllSubAccountsMsrofatJ($id) {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',1)
             ->where('Name','ILIKE', "%{$id}%")
           ->where('Parent',55)->pluck("Name","id");
   }else{

           $states = AcccountingManual::where('Type',1)
             ->where('NameEn','ILIKE', "%{$id}%")
           ->where('Parent',55)->pluck("NameEn","id");
   }

       return response()->json($states);

    }

         public function AllSubAccountsMwrden() {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',1)->where('Parent',37)->pluck("Name","id");
   }else{
       $states = AcccountingManual::where('Type',1)->where('Parent',37)->pluck("NameEn","id");

   }
       return response()->json($states);

    }

      public function AllSubAccountsMwrdenJ($id) {

   if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::where('Type',1)
             ->where('Name','ILIKE', "%{$id}%")
           ->where('Parent',37)->pluck("Name","id");
   }else{

      $states = AcccountingManual::where('Type',1)
             ->where('NameEn','ILIKE', "%{$id}%")
           ->where('Parent',37)->pluck("NameEn","id");
   }

       return response()->json($states);

    }


          public function AllCustomersJPhone($id) {



           if(is_numeric($id)){


               $states = Customers::
                where('Phone',$id)
                ->orWhere('Phone2',$id)
             ->pluck("Name","Account");


           }else{



    if(app()->getLocale() == 'ar' ){
       $states = AcccountingManual::
              where('Name','ILIKE', "%{$id}%")
              ->where('Type',1)
              ->where('Parent',24)
              ->pluck("Name","id");
    }else{

             $states = AcccountingManual::
              where('NameEn','ILIKE', "%{$id}%")
              ->where('Type',1)
              ->where('Parent',24)
              ->pluck("NameEn","id");
    }

               }

       return response()->json($states);

    }

     public function AllClientsJPhone($id) {

           if(is_numeric($id)){


               $states = Customers::
                where('Phone',$id)
                ->orWhere('Phone2',$id)
             ->pluck("Name","id");


           }else{
                    if(app()->getLocale() == 'ar' ){
               $states = Customers::
                where('Name','ILIKE', "%{$id}%")
                ->orWhere('Company_Name','ILIKE', "%{$id}%")
             ->pluck("Name","id");
                    }else{

                         $states = Customers::
                where('NameEn','ILIKE', "%{$id}%")
                ->orWhere('Company_Name','ILIKE', "%{$id}%")
             ->pluck("NameEn","id");
                    }
           }

       return response()->json($states);

    }



}
